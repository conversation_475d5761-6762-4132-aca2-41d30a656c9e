<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>文书电子送达系统</title>
	<link rel="stylesheet" href="../../static/css/head.css">
	<script src="../../static/js/vue2.js"></script>
	<!-- <link rel="stylesheet" href="../../static/css/<EMAIL>"> -->
	<script src="../../static/js/<EMAIL>"></script>
	<script src="../../static/js/axios.min.js"></script>
	<!-- <script src="https://cdn.jsdelivr.net/npm/vue@2/dist/vue.js"></script> -->
	<link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
	<!-- <script src="https://unpkg.com/element-ui/lib/index.js"></script> -->
</head>
<body>
	<div id="head">
		<el-row id="header">
		  <el-col :span="12" class="col-header">
			  <p id="title">通辽市奈曼旗公安局文书电子送达系统</p>
		  </el-col>
		  <el-col :span="12" class="col-header" >
			  <el-menu background-color="#3a85e0" text-color="#ffffff" active-text-color="#00fffb" :default-active="activeIndex" id="menu" mode="horizontal" @select="handleSelect">
			    <el-menu-item index="home">首页</el-menu-item>			
				<el-menu-item index="index">案件管理</el-menu-item>
			    <el-menu-item index="sendlog">送达记录</el-menu-item>
			    <!-- <el-menu-item index="setting">系统设置</el-menu-item> -->
				{if session('user.level') eq 0}
			    <el-menu-item index="user">成员管理</el-menu-item>
				{/if}
				<el-submenu index="4">
				    <template slot="title"><?php echo session('user.username'); ?></template>
					<el-menu-item index="mydata">我的信息</el-menu-item>
				    <el-menu-item index="exit" @click="exitVisible = true">退出登录</el-menu-item>
				</el-submenu>
			  </el-menu>
		  </el-col>
		</el-row>
		
		<el-dialog
		  title="退出"
		  :visible.sync="exitVisible"
		  width="30%"
		  >
		  <span>是否确认退出当前登录</span>
		  <span slot="footer" class="dialog-footer">
		    <el-button @click="exitVisible = false">取 消</el-button>
		    <el-button type="primary" @click="exit_user">确 定</el-button>
		  </span>
		</el-dialog>
	</div>
	
	
	<script src="../../static/js/head.js"></script>