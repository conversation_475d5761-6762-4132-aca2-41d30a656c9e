<?php

namespace app\index\controller;

use think\Controller;
use think\Db;

class Login extends Controller
{
    public function index()
    {
        return view();
    }
    // 验证密码
    public function verifyPassword($password, $hashedPassword) {
        return password_verify($password, $hashedPassword);
    }
    public function login(){
        if(request()->isAjax()){
        $data=[
            'username'=>input('post.username'),
            'password'=>input('post.password')
        ];
        $user=Db::name('user')->where('is_delete',0)->where('username',$data['username'])->find();
            if ($user && $this->verifyPassword($data['password'], $user['password'])) {
                $user=[
                    'username'=>$user['username'],
                    'name'=>$user['name'],
                    'level'=>$user['level'],
                    'create_time'=>$user['create_time'],
                    'update_time'=>$user['update_time'],
                ];
                session('user',$user);
                session('client_url',config('client_url'));
                //写操作日志
                $log=Db::name('log')->insert([
                    'username'=>session('user.username'),
                    'name'=>session('user.name'),
                    'action'=>'用户登录:'.request()->ip(),
                    'create_time'=>date('Y-m-d H:i:s', time()),
                ]);
                return json(['status' => 'success', 'message' => '登录成功']);
            }else{
                return json(['status' => 'error', 'message' => '用户名或密码错误']);
            }
        }else{
                        $log=Db::name('log')->insert([
                'username'=>session('user.username'),
                'name'=>session('user.name'),
                'action'=>'非法请求:'.request()->ip(),
                'create_time'=>date('Y-m-d H:i:s', time()),
            ]);
            return json(['status' => 'error', 'message' => '非法请求']);
        }

    }
    //退出登录
    public function exit_user (){
        //写操作日志
        $log=Db::name('log')->insert([
            'username'=>session('user.username'),
            'name'=>session('user.name'),
            'action'=>'用户主动退出登录:'.request()->ip(),
            'create_time'=>date('Y-m-d H:i:s', time()),
        ]);
        session(null);
        if (!session('?user.username')){
            return json(['status' => 'success', 'message' => '成功']);
        }else{
            return json(['status' => 'error', 'message' => '失败']);
        }

    }
}