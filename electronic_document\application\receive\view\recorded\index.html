<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
		<title>行政处罚决定书</title>
		<script src="../../static/js/vue2.js"></script>
		<!-- <link rel="stylesheet" href="../../static/css/<EMAIL>"> -->
		<script src="../../static/js/<EMAIL>"></script>
		<script src="../../static/js/axios.min.js"></script>
		<!-- <script src="https://cdn.jsdelivr.net/npm/vue@2/dist/vue.js"></script> -->
		<link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
		<!-- <script src="https://unpkg.com/element-ui/lib/index.js"></script> -->
		<link rel="stylesheet" href="../../static/css/recorded.css">

	</head>
	<body>
        <div id="app">
            <div v-if="permissionDenied" class="permission-denied">
                麦克风权限被拒绝，请点击<button @click="requestPermission">重试</button>
            </div>
            <el-row justify="center" class="row-class" align="top">
                <el-col :span="24">
                    <h2>请本人点击录制并清晰朗读下方内容</h2>
                    <p>我是{$data.receive}，手机号码是{$data.contact}，我已与{$data.time}收到电子文书，并已阅读，现在完成签署。</p>
                    <h2>{{ recordingTime }} 秒</h2>
                </el-col>
                <el-col class="buttons" :span="12">
                    <el-button @click="startRecording" :disabled="isRecording" type="primary">录制</el-button>
                </el-col>
                <el-col class="buttons" :span="12">
                    <el-button @click="stopRecording" :disabled="!isRecording" type="primary">停止</el-button>
                </el-col>
                <el-col class="buttons" :span="12">
                    <el-button @click="playRecording" :disabled="!audioChunks" type="primary">回放</el-button>
                </el-col>
                <el-col class="buttons" :span="12">
                    <el-button @click="uploadRecording" :disabled="!audioUrl" type="primary">确定上传</el-button>
                </el-col>
                <el-col class="buttons" :span="24">
                    <div class="btn-audio clear">
                        <div class="lf">
                            <p style="font-size: 1.2rem; color: #333;margin: 1rem 0 0.3rem 1.2rem;">文书确认</p>
                            <p style="font-size: 1rem; color: #888; margin: 0 0 0 1.2rem">确认签署录音</p>
                        </div>
                        <div class="mp3Box">
                            <audio :src="audioUrl" controls>
                            </audio>
                        </div>
                    </div>
                </el-col>

            </el-row>

        </div>
	</body>
    <script src="../../static/js/recorded.js"></script>
    <script src="../../static/js/js-audio-recorder.js"></script>
</html>

