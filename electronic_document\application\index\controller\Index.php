<?php
namespace app\index\controller;

use app\common\GaiValidate;
use app\common\service\Download;
use app\common\service\SmsService;
use app\common\Validate;
use app\common\SendValidate;
use think\Controller;
use think\Db;
use think\Request;
use think\Session;

class Index extends Controller
{
    public function initialize()
    {
        if(!session('?user.username')){
            $this->redirect('index/login/index');
        }
    }
    public function index()
    {
        return view();
    }
    public function addfile(Request $request)
    {
        // 获取上传的文件
        $file = $request->file('file');
        //获取后缀
        $info=$file->getInfo();
        $suffix=explode('.',$info['name']);
        $suffix=array_pop($suffix);
        $imgs=['jpg', 'jpeg', 'png', 'gif'];
        $audios=['mp3', 'm4a'];
        if (in_array($suffix,$imgs)){
            $class='img';
        }elseif (in_array($suffix,$audios)){
            $class='audio';
        }else{
            $class='other';
        }
        // 初始化一个空数组来存储上传结果
        $uploadResults = [];
        // 检查是否有文件上传
        if ($file) {
            // 获取应用程序的根目录路径
            $rootPath = app()->getRootPath();
            // 使用 dirname() 函数获取上一级目录
            $desiredPath = dirname($rootPath);
            $uploadDir=$desiredPath.'/electronic_document_client/public/upload/'.$class.'/';
//                $uploadDir ='E:/code/electronic_document_client/public/static/upload/'.$class.'/';
//                $uploadDir_client ='E:/code/electronic_document_client/public/static/upload/'.$class.'/';

                // 确保上传目录存在
                if (!is_dir($uploadDir)) {
                    mkdir($uploadDir, 0777, true);
                }
                // 生成文件名
            $fileName = md5(uniqid()) .'.'. $suffix;
            // 移动文件到目标目录
                $info = $file->move($uploadDir, $fileName);

                // 存储上传结果
            if ($info) {
                    $uploadResults= [
                        'status' => 'success',
                        'message' => '文件上传成功',
                        'client_url'=>config('client_url'),
                        'filePath' => '/upload/'.$class.'/' . $info->getSaveName() // 使用 getSaveName() 获取保存后的文件名（包括路径，但这里只返回相对路径）
                    ];
                } else {
                    $uploadResults = [
                        'status' => 'error',
                        'message' => $file->getError()
                    ];
                }
        } else {
            // 如果没有文件上传或文件字段不正确
            $uploadResults[] = [
                'status' => 'error',
                'message' => '没有文件被上传或文件字段不正确'
            ];
        }

        //写操作日志
        $log=Db::name('log')->insert([
            'username'=>session('user.username'),
            'name'=>session('user.name'),
            'action'=>'上传文件：'.$uploadResults['message'],
            'create_time'=>date('Y-m-d H:i:s', time()),
        ]);
        // 返回所有文件的上传结果
        return json($uploadResults);
    }
    public function addcase(){
        if(request()->isAjax()){
            $uid=md5(uniqid());

            $case=[
                'uid'=>$uid,
                'casenumber' => input('post.casenumber'),
                'title' => input('post.title'),
                'receive' => input('post.receive'),
                'contact' => input('post.contact'),
                'e_send' => input('post.e_send', '1'),
                'remark' => input('post.remark'),
                'server' => input('post.server'),
                'create_user'=>session('user.username'),
                'create_time'=>date('Y-m-d H:i:s', time()),
                'update_time'=>date('Y-m-d H:i:s', time()),
            ];
            $filelist=input('post.fileList');
            if (!$filelist){
                return json(['status' => 'error','message'=>'请上传文件']);
            }
            $validate=new Validate();
            if (!$validate->check($case)) {
                // 验证失败，输出错误信息
                return json(['status' => 'error','message'=>$validate->getError()]);
            }else{
                $if_exist=Db::name('case')->where('casenumber',$case['casenumber'])->where('is_delete',0)->find();
                if ($if_exist){
                    return json(['status' => 'error','message'=>'案件编号已存在']);
                }else{
                    $files=[];
                    foreach ($filelist as $file){
                        $files[]=[
                          'uid'=>$uid,
                          'casenumber'=>$case['casenumber'],
                          'class'=>0,
                          'name'=> $file['audioname'],
                          'path'=>  $file['path'],
                          'create_time'=>date('Y-m-d H:i:s', time()),
                        ];
                    }
                    //写入送达人
                    foreach ($case['server'] as $server){
                        $insert_server=Db::name('server')->insert([
                            'server'=>$server,
                            'uid'=>$uid,
                            'casenumber' => $case['casenumber'],
                            'create_time'=>date('Y-m-d H:i:s', time()),
                            'update_time'=>date('Y-m-d H:i:s', time()),
                        ]);
                    }
                    // 使用 unset() 函数删除 'server' 键
                    unset($case['server']);
                    $insert_file=Db::name('file')->insertAll($files);
                    $insert_case=Db::name('case')->insert($case);
                    if ($insert_case&&$insert_file&&$insert_server){
                        $log=Db::name('log')->insert([
                            'username'=>session('user.username'),
                            'name'=>session('user.name'),
                            'action'=>'新增案件:'.$case['casenumber'],
                            'create_time'=>date('Y-m-d H:i:s', time()),
                        ]);
                        return json(['status' => 'success','message'=>'新建案件成功']);
                    }else{
                        return json(['status' => 'error', 'message' => '网络异常']);
                    }
                }
            }

        }else{
            //写操作日志
            $log=Db::name('log')->insert([
                'username'=>session('user.username'),
                'name'=>session('user.name'),
                'action'=>'非法请求:'.request()->ip(),
                'create_time'=>date('Y-m-d H:i:s', time()),
            ]);
            return json(['status' => 'error', 'message' => '非法请求']);
        }
    }
    public function xiugai_case(){
        if(request()->isAjax()){
            $uid=input('post.uid');
            $casenumber=input('post.casenumber');
            $case=[
                'title' => input('post.title'),
                'receive' => input('post.receive'),
                'contact' => input('post.contact'),
                'server' => input('post.server'),
                'e_send' => input('post.e_send'),
                'remark' => input('post.remark'),
                'update_time'=>date('Y-m-d H:i:s', time()),
            ];
            $filelist=input('post.fileList');
            if (!$filelist){
                return json(['status' => 'error','message'=>'请上传文件']);
            }
            $validate=new GaiValidate();
            if (!$validate->check($case)) {
                // 验证失败，输出错误信息
                return json(['status' => 'error','message'=>$validate->getError()]);
            }else{

                    $files=[];
                    foreach ($filelist as $file){
                        $files[]=[
                            'uid'=>$uid,
                            'casenumber'=>$casenumber,
                            'class'=>0,
                            'name'=> $file['audioname'],
                            'path'=>  $file['path'],
                            'create_time'=>date('Y-m-d H:i:s', time()),
                        ];
                    }
                    //删除这批文件然后再更新
                    $delete=Db::name('file')->where('uid',$uid)->where('casenumber',$casenumber)->delete();
                    if ($delete){
                        //删除之前写的送达人
                        $delete=Db::name('server')->where('uid',$uid)->where('casenumber',$casenumber)->update(['is_delete'=>1]);
                        //写入送达人
                        foreach ($case['server'] as $server){
                            $insert_server=Db::name('server')->insert([
                                'server'=>$server,
                                'uid'=>$uid,
                                'casenumber' => $casenumber,
                                'create_time'=>date('Y-m-d H:i:s', time()),
                                'update_time'=>date('Y-m-d H:i:s', time()),
                            ]);
                        }
                        // 使用 unset() 函数删除 'server' 键
                        unset($case['server']);
                        $insert_file=Db::name('file')->insertAll($files);
                        $insert_case=Db::name('case')->where('uid',$uid)->where('casenumber',$casenumber)->update($case);
                        //写操作日志
                        $log=Db::name('log')->insert([
                            'username'=>session('user.username'),
                            'name'=>session('user.name'),
                            'action'=>'修改案件:'.$casenumber,
                            'create_time'=>date('Y-m-d H:i:s', time()),
                        ]);
                        return json(['status' => 'success','message'=>'修改案件成功']);
                    }else{
                        return json(['status' => 'error', 'message' => '修改1失败']);
                    }

            }

        }else{
                        $log=Db::name('log')->insert([
                'username'=>session('user.username'),
                'name'=>session('user.name'),
                'action'=>'非法请求:'.request()->ip(),
                'create_time'=>date('Y-m-d H:i:s', time()),
            ]);
            return json(['status' => 'error', 'message' => '非法请求']);
        }
    }
    public function addsend(){
        if(request()->isAjax()){
            //给送达单独做一个uid以便与file结合
//            $uid=md5(uniqid());
            $uid=round(microtime(true) * 1000);
            $send=[
                'uid'=>$uid,
                'casenumber' => input('post.casenumber'),
                'class' => input('post.writ_class', '1'),
                'text' => input('post.send_text'),
                'writ_title'=>input('post.writ_title'),
                'writ_number'=>input('post.writ_number'),
                'writ_function'=>input('post.writ_function'),
                'remark' => input('post.remark'),
                'server' => input('post.server'),
                'create_user'=>session('user.username'),
                'create_time'=>date('Y-m-d H:i:s', time()),
                'update_time'=>date('Y-m-d H:i:s', time()),
            ];
            $filelist=input('post.fileList');
            if (!$filelist){
                return json(['status' => 'error','message'=>'请上传文件']);
            }
            $validate=new SendValidate();
            if (!$validate->check($send)) {
                // 验证失败，输出错误信息
                return json(['status' => 'error','message'=>$validate->getError()]);
            }else{
                    $files=[];
                    foreach ($filelist as $file){
                        $files[]=[
                            'casenumber'=>$send['casenumber'],
                            'uid'=>$uid,
                            'class'=>1,
                            'name'=> $file['imgname'],
//                            'uploadwrit'=>1,
                            'path'=>  $file['path'],
                            'create_time'=>date('Y-m-d H:i:s', time()),
                        ];
                    }
                    //发送短信
                    $num=Db::name('case')->where('casenumber',$send['casenumber'])->where('is_delete',0)->value('contact');
                    $receive=Db::name('case')->where('is_delete',0)->where('casenumber',$send['casenumber'])->value('receive');
                    $type=$send['class'];
                if ($type == 1) {
                    $type = '行政案件立案告知书';
                } elseif ($type == 2) {
                    $type = '不予立案告知书';
                } elseif ($type == 3) {
                    $type = '证据保全决定书';
                } elseif ($type == 4) {
                    $type = '行政处罚告知笔录';
                } elseif ($type == 5) {
                    $type = '鉴定意见通知书';
                } elseif ($type == 6) {
                    $type = '不予行政处罚决定书';
                } elseif ($type == 7) {
                    $type = '行政处罚决定书';
                } elseif ($type == 8) {
                    $type = '责令限期改正通知书';
                } elseif ($type == 9) {
                    $type = '没收违法所得、非法财物清单';
                } elseif ($type == 10) {
                    $type = '收缴物品清单';
                } elseif ($type == 11) {
                    $type = '追缴物品清单';
                } elseif ($type == 12) {
                    $type = '催告书';
                } elseif ($type == 13) {
                    $type = '终止案件调查决定书';
                } else {
                    $type = '未知';
                }
                $data=[
                    'uid'=>$uid,
                    'receive'=>$receive,
                    'time'=>date('Y年m月d日'),
                    'type'=>$type
                ];
                    $sendsms=(new SmsService)->sendsms($num,$data);
                    if ($sendsms['code']==0){
                        $send['reach_time']=date('Y-m-d H:i:s', time());
                        //先上传送达人
                        //写入送达人
                        foreach ($send['server'] as $server){
                            $insert_server=Db::name('server')->insert([
                                'server'=>$server,
                                'uid'=>$uid,
                                'casenumber' => $send['casenumber'],
                                'create_time'=>date('Y-m-d H:i:s', time()),
                                'update_time'=>date('Y-m-d H:i:s', time()),
                            ]);
                        }
                        // 使用 unset() 函数删除 'server' 键
                        unset($send['server']);
                        $insert_file=Db::name('file')->insertAll($files);
                        $insert_case=Db::name('send')->insert($send);
                        if ($insert_case&&$insert_file){
                            //写操作日志
                            $log=Db::name('log')->insert([
                                'username'=>session('user.username'),
                                'name'=>session('user.name'),
                                'action'=>'新建送达'.$uid,
                                'create_time'=>date('Y-m-d H:i:s', time()),
                            ]);
                            return json(['status' => 'success','message'=>'新建送达成功']);
                        }else{
                            return json(['status' => 'error','message'=>'短信发送成功，但送达保存失败']);
                        }
                    }else{
                        return json(['status' => 'error', 'message' => '短信发送失败：'.$sendsms['msg']]);

                    }


            }

        }else{
                        $log=Db::name('log')->insert([
                'username'=>session('user.username'),
                'name'=>session('user.name'),
                'action'=>'非法请求:'.request()->ip(),
                'create_time'=>date('Y-m-d H:i:s', time()),
            ]);
            return json(['status' => 'error', 'message' => '非法请求']);
        }
    }
    public function select_case(){
        if(request()->isAjax()){
            $search=[
                'casenumber' => input('post.casenumber'),
                'title' => input('post.title'),
                'receive' => input('post.receive'),
                'contact' => input('post.contact'),
                'signed_status' => input('post.signed_status'),
                'time' => input('post.time'),
            ];
            $where=[];
            if ($search['casenumber']){
                $where[]=[
                    'c.casenumber','like', '%' . $search['casenumber'] . '%'
                ];
            }
            if ($search['title']){
                $where[]=[
                    'c.title','like', '%' . $search['title'] . '%'
                ];
            }
            if ($search['receive']){
                $where[]=[
                    'c.receive','like', '%' . $search['receive'] . '%'
                ];
            }
            if ($search['contact']){
                $where[]=[
                    'c.contact','like', '%' . $search['contact'] . '%'
                ];
            }
            if (session('user.level')!=0){
                $where[]=['c.create_user','=',session('user.username')];
            }

            // 获取所有符合条件的 case 数据
            $cases = Db::name('case')
                ->alias('c')
                ->join('user u', 'c.create_user = u.username', 'LEFT')
//                ->join('server v', 'c.uid = v.uid', 'LEFT')
//                ->where('v.is_delete', 0)
                ->where('c.is_delete', 0)
                ->where('u.is_delete', 0)
                ->field('c.* ,u.name as u_name')
                ->orderRaw("c.create_time DESC")
                ->where($where)
                ->select();
//                ->toArray();
            $data = [];

            foreach ($cases as $case) {
                $casenumber = $case['casenumber'];

                // 获取 send 表中最新的一条记录
                $latestSend = Db::name('send')
                    ->where('casenumber', $casenumber)
                    ->where('is_delete', 0)
                    ->order('create_time', 'desc')
                    ->limit(1)
                    ->field('reach_time, receive_time, status')
                    ->find();
                //匹配搜索内容
                if ($search['signed_status']!='all'&&$latestSend['status']!=$search['signed_status']){
                    continue;
                }
                if ($search['time']){
                    // 解析时间字符串为 DateTime 对象
                    $startTime = new \DateTimeImmutable($search['time'][0]);
                    $endTime = new \DateTimeImmutable($search['time'][1]);
                    // 将 DateTime 对象转换为数据库可以识别的格式（通常是字符串）
                    $startTimeStr = $startTime->format('Y-m-d H:i:s');
                    $endTimeStr = $endTime->format('Y-m-d H:i:s');
                    if ($latestSend['receive_time']<$startTimeStr||$latestSend['receive_time']>$endTimeStr){
                        continue;
                    }
                }
                // 计算 send 表中符合条件的记录条数
                $sendCount = Db::name('send')
                    ->where('casenumber', $casenumber)
                    ->where('is_delete', 0)
                    ->count();

                //查找送达人表
                $server=Db::name('server')
                    ->where('casenumber', $casenumber)
                    ->where('is_delete', 0)
                    ->column('server');
                // 组装结果
                $data[] = array_merge($case, [
                    'reach_time' => $latestSend['reach_time'] ?? null,
                    'receive_time' => $latestSend['receive_time'] ?? null,
                    'status' => $latestSend['status'] ?? null,
                    'send_count' => $sendCount,
                    'server'=>$server
                ]);
            }

            return json(['status' => 'success', 'message' => [
                'total'=>sizeof($data),
                'data'=>$data
            ]]);

        } else{
                        $log=Db::name('log')->insert([
                'username'=>session('user.username'),
                'name'=>session('user.name'),
                'action'=>'非法请求:'.request()->ip(),
                'create_time'=>date('Y-m-d H:i:s', time()),
            ]);
            return json(['status' => 'error', 'message' => '非法请求']);
        }
    }
    public function select_send(){
        if(request()->isAjax()){
            $casenumber=input('post.casenumber');

            $sends=Db::name('send')
                        ->alias('s')
//                        ->join('server v', 's.uid = v.uid', 'LEFT')
//                        ->field('s.* ,v.server as server')
//                        ->where('v.is_delete',0)
                        ->where('s.is_delete',0)
                        ->where('s.casenumber',$casenumber)
                        ->orderRaw("s.create_time DESC")
                        ->select();

            $data=[];
            foreach ($sends as $send) {
                $uid = $send['uid'];
                $server = Db::name('server')
                    ->where('uid', $uid)
                    ->where('is_delete', 0)
                    ->order('create_time', 'desc')
                    ->column('server');
                // 组装结果
                $data[] = array_merge($send, [
                    'server' => $server ?? null,
                ]);
            }

            return json(['status' => 'success', 'message' => $data]);
        } else{
                        $log=Db::name('log')->insert([
                'username'=>session('user.username'),
                'name'=>session('user.name'),
                'action'=>'非法请求:'.request()->ip(),
                'create_time'=>date('Y-m-d H:i:s', time()),
            ]);
            return json(['status' => 'error', 'message' => '非法请求']);
        }
    }
    public function download_case(){
        if(request()->isAjax()){
            $time=input('post.time');
            if (!$time){
                return json(['status' => 'error', 'message' => '请选择时间']);
            }else{
                $download=(new Download())->download('case',$time);
                if ($download){
                    //写操作日志
                    $log=Db::name('log')->insert([
                        'username'=>session('user.username'),
                        'name'=>session('user.name'),
                        'action'=>'导出案件:'.$time,
                        'create_time'=>date('Y-m-d H:i:s', time()),
                    ]);
                    return json(['status' => 'success', 'message' => $download]);
                }else{
                    return json(['status' => 'error', 'message' => '导出失败']);
                }
            }
        } else{
                        $log=Db::name('log')->insert([
                'username'=>session('user.username'),
                'name'=>session('user.name'),
                'action'=>'非法请求:'.request()->ip(),
                'create_time'=>date('Y-m-d H:i:s', time()),
            ]);
            return json(['status' => 'error', 'message' => '非法请求']);
        }
    }
    public function delete_case(){
        if(request()->isAjax()){
            $casenumber=input('post.casenumber');
            //查询是否已经有送达记录，如果有就不能删除
            $sendlog=Db::name('send')->where('is_delete',0)->where('casenumber',$casenumber)->find();
            if ($sendlog){
                return json(['status' => 'error', 'message' => '本案件已有送达记录，不可删除']);
            }else{
                $res=Db::name('case')->where('casenumber',$casenumber)->where('is_delete',0)->update(['is_delete'=>1,'casenumber'=>'delete'.$casenumber]);
                if ($res){
                    //写操作日志
                    $log=Db::name('log')->insert([
                        'username'=>session('user.username'),
                        'name'=>session('user.name'),
                        'action'=>'删除案件:'.$casenumber,
                        'create_time'=>date('Y-m-d H:i:s', time()),
                    ]);
                    return json(['status' => 'success', 'message' => '删除成功']);
                }else{
                    return json(['status' => 'error', 'message' => '无此案件']);
                }
            }

        } else{
                        $log=Db::name('log')->insert([
                'username'=>session('user.username'),
                'name'=>session('user.name'),
                'action'=>'非法请求:'.request()->ip(),
                'create_time'=>date('Y-m-d H:i:s', time()),
            ]);
            return json(['status' => 'error', 'message' => '非法请求']);
        }
    }
    public function getfiles(){
        if(request()->isAjax()){
            $casenumber=input('post.casenumber');
            $uid=input('post.uid');

            $files=Db::name('file')->where('is_delete',0)->where('casenumber',$casenumber)->where('uid',$uid)->field('name,path as url,uid as uuid')->select();
            // 遍历结果集并修改 url 字段
            foreach ($files as &$file) {
                $file['url'] = config('client_url') . $file['url'];
            }
            return json(['status' => 'success', 'message' => $files]);


        } else{
            $log=Db::name('log')->insert([
                'username'=>session('user.username'),
                'name'=>session('user.name'),
                'action'=>'非法请求:'.request()->ip(),
                'create_time'=>date('Y-m-d H:i:s', time()),
            ]);
            return json(['status' => 'error', 'message' => '非法请求']);
        }
    }

}
