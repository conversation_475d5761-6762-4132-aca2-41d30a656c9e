<?php

namespace app\index\controller;

use app\common\UserGaiValidate;
use app\common\UserValidate;
use app\common\Validate;
use think\Controller;
use think\Db;

class User extends Controller
{
    public function initialize()
    {
        if(!session('?user.username')){
            $this->redirect('index/login/index');
        }
    }
    public function index(){
        return view();
    }
    public function select_user(){
        if(request()->isAjax()){
            $search=[
                'username' => input('post.username'),
                'name' => input('post.name'),
                'level' => input('post.level'),
            ];
            $where=[];
            if ($search['username']){
                $where[]=[
                    'username','like', '%' . $search['username'] . '%'
                ];
            }
            if ($search['name']){
                $where[]=[
                    'name','like', '%' . $search['name'] . '%'
                ];
            }
            if ($search['level']){
                if ($search['level']!='all'){
                    $where[]=[
                        'level','like', '%' . $search['level'] . '%'
                    ];
                }
            }


            $data=Db::name('user')
                ->field('username, name ,level,is_delete as status')
//                ->where('is_delete',0)
                ->where($where)
                ->orderRaw("create_time DESC")
                ->select();
            return json(['status' => 'success', 'message' => [
                'total'=>sizeof($data),
                'data'=>$data
            ]]);

        } else{
                        $log=Db::name('log')->insert([
                'username'=>session('user.username'),
                'name'=>session('user.name'),
                'action'=>'非法请求:'.request()->ip(),
                'create_time'=>date('Y-m-d H:i:s', time()),
            ]);
            return json(['status' => 'error', 'message' => '非法请求']);
        }
    }

//    public function adduser(){
//        if(request()->isAjax()){
//            $user=[
//                'username' => input('post.username'),
//                'name' => input('post.name'),
//                'password' => input('post.password'),
//                'level' => input('post.level'),
//                'create_time'=>date('Y-m-d H:i:s', time()),
//                'update_time'=>date('Y-m-d H:i:s', time()),
//            ];
//            $validate=new UserValidate();
//            if (!$validate->check($user)) {
//                // 验证失败，输出错误信息
//                return json(['status' => 'error','message'=>$validate->getError()]);
//            }else{
//                $if_exist=Db::name('user')->where('username',$user['username'])->find();
//                if ($if_exist){
//                    return json(['status' => 'error','message'=>'用户名已存在']);
//                }else{
//                    $insert_user=Db::name('user')->insert($user);
//                    if ($insert_user){
//                        return json(['status' => 'success','message'=>'新建成员成功']);
//                    }else{
//                        return json(['status' => 'error', 'message' => '网络异常']);
//                    }
//                }
//            }
//
//        }else{
//                        $log=Db::name('log')->insert([
//                'username'=>session('user.username'),
//                'name'=>session('user.name'),
//                'action'=>'非法请求:'.request()->ip(),
//                'create_time'=>date('Y-m-d H:i:s', time()),
//            ]);
//            return json(['status' => 'error', 'message' => '非法请求']);
//        }
//    }
    public function delete_user(){
        if(request()->isAjax()){
            $username=input('post.username');
            if ($username=='admin'){
                return json(['status' => 'error', 'message' => 'admin用户不可删除！']);
            }
            //查询是否已经有创建案件记录，如果有就不能删除
            $case=Db::name('case')->where('is_delete',0)->where('create_user',$username)->find();
            if ($case){
                $res=Db::name('user')->where('is_delete',0)->where('username',$username)->update(['is_delete'=>1]);
                if ($res){
                    //写操作日志
                    $log=Db::name('log')->insert([
                        'username'=>session('user.username'),
                        'name'=>session('user.name'),
                        'action'=>'删除用户:'.$username.'本用户已创建过案件，不可删除,已将该用户停用',
                        'create_time'=>date('Y-m-d H:i:s', time()),
                    ]);
                    return json(['status' => 'success', 'message' => '本用户已创建过案件，不可删除,已将该用户停用']);
                }else{
                    return json(['status' => 'error', 'message' => '无此成员']);
                }
            }else{
                $res=Db::name('user')->where('is_delete',0)->where('username',$username)->delete();
                if ($res){
                    //写操作日志
                    $log=Db::name('log')->insert([
                        'username'=>session('user.username'),
                        'name'=>session('user.name'),
                        'action'=>'删除用户:'.$username.'删除成功',
                        'create_time'=>date('Y-m-d H:i:s', time()),
                    ]);
                    return json(['status' => 'success', 'message' => '删除成功']);
                }else{
                    return json(['status' => 'error', 'message' => '无此成员']);
                }
            }

        } else{
                        $log=Db::name('log')->insert([
                'username'=>session('user.username'),
                'name'=>session('user.name'),
                'action'=>'非法请求:'.request()->ip(),
                'create_time'=>date('Y-m-d H:i:s', time()),
            ]);
            return json(['status' => 'error', 'message' => '非法请求']);
        }
    }
    public function open_user(){
        if(request()->isAjax()){
            $username=input('post.username');
            $res=Db::name('user')->where('is_delete',1)->where('username',$username)->update(['is_delete'=>0]);
            if ($res){
                //写操作日志
                $log=Db::name('log')->insert([
                    'username'=>session('user.username'),
                    'name'=>session('user.name'),
                    'action'=>'启用用户:'.$username,
                    'create_time'=>date('Y-m-d H:i:s', time()),
                ]);
                return json(['status' => 'success', 'message' => '启用成功']);
            }else{
                return json(['status' => 'error', 'message' => '无此成员']);
            }
        } else{
                        $log=Db::name('log')->insert([
                'username'=>session('user.username'),
                'name'=>session('user.name'),
                'action'=>'非法请求:'.request()->ip(),
                'create_time'=>date('Y-m-d H:i:s', time()),
            ]);
            return json(['status' => 'error', 'message' => '非法请求']);
        }
    }
    public function xiugai_user(){
        if(request()->isAjax()){
            $username=input('post.username');
            $password=input('post.password');
            $data=[
                'name'=>input('post.name'),
                'level'=>input('post.level'),
                'update_time'=>date('Y-m-d H:i:s', time())
            ];
            //数据校验
            $validate=new UserGaiValidate();
            if (!$validate->check($data)) {
                // 验证失败，输出错误信息
                return json(['status' => 'error','message'=>$validate->getError()]);
            }else{
                if ($password){
                    $data['password']=password_hash($password, PASSWORD_DEFAULT);;
                }
                //直接修改，不判断是否有修改
                $res=Db::name('user')->where('username',$username)->update($data);
                //写操作日志
                $log=Db::name('log')->insert([
                    'username'=>session('user.username'),
                    'name'=>session('user.name'),
                    'action'=>'修改用户:'.$username,
                    'create_time'=>date('Y-m-d H:i:s', time()),
                ]);
                return json(['status' => 'success', 'message' => '修改成功']);
            }

        } else{
                        $log=Db::name('log')->insert([
                'username'=>session('user.username'),
                'name'=>session('user.name'),
                'action'=>'非法请求:'.request()->ip(),
                'create_time'=>date('Y-m-d H:i:s', time()),
            ]);
            return json(['status' => 'error', 'message' => '非法请求']);
        }
    }

    public function verifyPassword($password, $hashedPassword) {
        return password_verify($password, $hashedPassword);
    }
    public function xiugai_password(){
        if(request()->isAjax()){
            $old=input('post.old');
            $new=input('post.new');
            $repeat=input('post.repeat');
            //判断新密码
            if($new!=$repeat){
                return json(['status' => 'error', 'message' => '两次输入密码不一致']);
            }
            if ($new==$old){
                return json(['status' => 'error', 'message' => '原密码和新密码不能相同']);
            }
            $password=Db::name('user')->where('is_delete',0)->where('username',session('user.username'))->value('password');
            //判断旧密码
            if ($password&&$this->verifyPassword($old, $password)){
                $res=Db::name('user')->where('is_delete',0)->where('username',session('user.username'))->update(['password'=>password_hash($new, PASSWORD_DEFAULT)]);
                if ($res){
                    session(null);
                    return json(['status' => 'success', 'message' => '修改成功']);
                }else{
                    return json(['status' => 'error', 'message' => '无此用户']);
                }
            }else{
                return json(['status' => 'error', 'message' => '原密码输入错误']);
            }

        } else{
                        $log=Db::name('log')->insert([
                'username'=>session('user.username'),
                'name'=>session('user.name'),
                'action'=>'非法请求:'.request()->ip(),
                'create_time'=>date('Y-m-d H:i:s', time()),
            ]);
            return json(['status' => 'error', 'message' => '非法请求']);
        }
    }
}