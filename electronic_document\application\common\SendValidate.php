<?php

namespace app\common;

class SendValidate extends \think\Validate
{
// 定义验证规则
    protected $rule = [
        'class'      => 'require|in:0,1,2,3,4,5,6,7,8,9,10,11,12,13',
        'text'      => 'require',
        'server'      => 'require',
        'writ_title'      => 'require',
        'writ_number'      => 'require',
        'writ_function'      => 'require',


    ];

    // 定义错误消息
    protected $message = [
        'class.require'     => '文书类型不能为空',
        'class.in'          => '文书类型有误',
        'text.require'     => '发送内容不能为空',
        'server.require'     => '送达人不能为空',
        'writ_title.require'     => '文书名称不能为空',
        'writ_number.require'     => '文书编号不能为空',
        'writ_function.require'     => '如何救济途径信息不能为空',


    ];
}