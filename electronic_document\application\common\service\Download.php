<?php

namespace app\common\service;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use think\Db;

class Download
{
    //设置导出规则
    public function download($type,$time){

        // 解析时间字符串为 DateTime 对象
        $startTime = new \DateTimeImmutable($time[0]);
        $endTime = new \DateTimeImmutable($time[1]);
        // 将 DateTime 对象转换为数据库可以识别的格式（通常是字符串）
        $startTimeStr = $startTime->format('Y-m-d H:i:s');
        $endTimeStr = $endTime->format('Y-m-d H:i:s');

        // 创建一个新的 Excel 文件
        $spreadsheet = new Spreadsheet();

        $sheet = $spreadsheet->getActiveSheet();

        //设置宽度
        $sheet->getColumnDimension('A')->setWidth(20);
        $sheet->getColumnDimension('B')->setWidth(20);
        $sheet->getColumnDimension('C')->setWidth(20);
        $sheet->getColumnDimension('D')->setWidth(20);
        $sheet->getColumnDimension('E')->setWidth(20);
        $sheet->getColumnDimension('F')->setWidth(20);
        $sheet->getColumnDimension('G')->setWidth(20);
        $sheet->getColumnDimension('H')->setWidth(20);
        $sheet->getColumnDimension('I')->setWidth(20);
        $sheet->getColumnDimension('J')->setWidth(20);
        $sheet->getColumnDimension('k')->setWidth(20);
        //设置居中
        $sheet->getStyle('A')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $sheet->getStyle('B')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $sheet->getStyle('C')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $sheet->getStyle('D')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $sheet->getStyle('E')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $sheet->getStyle('F')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $sheet->getStyle('G')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $sheet->getStyle('H')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $sheet->getStyle('I')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $sheet->getStyle('J')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $sheet->getStyle('k')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        // 创建一个样式对象
//        $title = [
//            'font' => [
//                'bold' => true,
//            ],
//            'alignment' => [
//                'horizontal' => Alignment::HORIZONTAL_CENTER,
//                'vertical' => Alignment::VERTICAL_CENTER,
//            ],
//        ];
//        $text=[
//            'alignment' => [
//                'horizontal' => Alignment::HORIZONTAL_CENTER,
//                'vertical' => Alignment::VERTICAL_CENTER,
//            ],
//        ];
        $row=3;
        if ($type=='case'){
            $sheet->mergeCells('A1:J1');//合并单元格
            $sheet->setCellValue('A1', $startTimeStr.'~'.$endTimeStr . '案件管理导出表，导出时间：'.date('Y-m-d H:i:s', time()));
            $sheet->setCellValue('A2', '序号');
            $sheet->setCellValue('B2', '案件编号');
            $sheet->setCellValue('C2', '案件名称');
            $sheet->setCellValue('D2', '接收人姓名');
            $sheet->setCellValue('E2', '接收人联系方式');
            $sheet->setCellValue('F2', '发送次数');
            $sheet->setCellValue('G2', '最近一次送达时间');
            $sheet->setCellValue('H2', '最近一次送达状态');
            $sheet->setCellValue('I2', '最近一次签收时间');
            $sheet->setCellValue('J2', '备注');

            //获取写入数据
            // 获取所有符合条件的 case 数据
            $cases = Db::name('case')
                ->alias('c')
                ->join('user u', 'c.create_user = u.username', 'LEFT')
                ->where('c.is_delete', 0)
                ->where('u.is_delete', 0)
                ->field('c.* ,u.name as u_name')
                ->orderRaw("c.create_time DESC")
                ->where('c.create_time','>=',$startTimeStr)
                ->where('c.create_time','<=',$endTimeStr)
                ->select();
            $data = [];
            foreach ($cases as $case) {
                $casenumber = $case['casenumber'];
                // 获取 send 表中最新的一条记录
                $latestSend = Db::name('send')
                    ->where('casenumber', $casenumber)
                    ->where('is_delete', 0)
                    ->order('create_time', 'desc')
                    ->limit(1)
                    ->field('reach_time, receive_time, status')
                    ->find();
                // 计算 send 表中符合条件的记录条数
                $sendCount = Db::name('send')
                    ->where('casenumber', $casenumber)
                    ->where('is_delete', 0)
                    ->count();
                // 组装结果
                $data[] = array_merge($case, [
                    'reach_time' => $latestSend['reach_time'] ?? null,
                    'receive_time' => $latestSend['receive_time'] ?? null,
                    'status' => $latestSend['status'] ?? null,
                    'send_count' => $sendCount,
                ]);
                //循环写入数据
                foreach ($data as $value) {
                    //送达状态格式化
                    if ($value['status']==0){
                        $value['status']='未签收';
                    }elseif ($value['status']==1){
                        $value['status']='已签收';
                    }elseif ($value['status']==='null'){
                        $value['status']='未发送';
                    }else{
                        $value['status']='未知';
                    }
                    $sheet->setCellValue('A' . $row, $row-2);
                    $sheet->setCellValue('B' . $row, $value['casenumber']);
                    $sheet->setCellValue('C' . $row, $value['title']);
                    $sheet->setCellValue('D' . $row, $value['receive']);
                    $sheet->setCellValue('E' . $row, $value['contact']);
                    $sheet->setCellValue('F' . $row, $value['send_count']);
                    $sheet->setCellValue('G' . $row, $value['reach_time']);
                    $sheet->setCellValue('H' . $row, $value['status']);
                    $sheet->setCellValue('I' . $row, $value['receive_time']);
                    $sheet->setCellValue('J' . $row, $value['remark']);
                    $row++;
                }

            }

        }elseif ($type=='send'){
            $sheet->mergeCells('A1:K1');//合并单元格
            $sheet->setCellValue('A1', $startTimeStr.'~'.$endTimeStr . '送达记录导出表，导出时间：'.date('Y-m-d H:i:s', time()));
            $sheet->setCellValue('A2', '序号');
            $sheet->setCellValue('B2', '案件编号');
            $sheet->setCellValue('C2', '案件名称');
            $sheet->setCellValue('D2', '接收人姓名');
            $sheet->setCellValue('E2', '接收人联系方式');
            $sheet->setCellValue('F2', '文书类型');
            $sheet->setCellValue('G2', '发送内容');
            $sheet->setCellValue('H2', '上传文书');
            $sheet->setCellValue('I2', '送达时间');
            $sheet->setCellValue('J2', '签收时间');
            $sheet->setCellValue('K2', '状态');
            //获取写入数据
            $data=Db::name('send')
                ->alias('s')
                ->join('case c', 'c.casenumber = s.casenumber', 'LEFT')
                ->field('s.uid as s_uid ,s.* ,c.title,c.contact,c.receive')
                ->where('s.is_delete',0)
                ->where('c.is_delete',0)
                ->where('c.create_time','>=',$startTimeStr)
                ->where('c.create_time','<=',$endTimeStr)
                ->orderRaw("s.create_time DESC")
                ->select();
            //循环写入数据
            foreach ($data as $value) {
                //上传文书，状态，文书类型格式化
                if ($value['class'] == 1) {
                    $value['class'] = '行政案件立案告知书';
                } elseif ($value['class'] == 2) {
                    $value['class'] = '不予立案告知书';
                } elseif ($value['class'] == 3) {
                    $value['class'] = '证据保全决定书';
                } elseif ($value['class'] == 4) {
                    $value['class'] = '行政处罚告知笔录';
                } elseif ($value['class'] == 5) {
                    $value['class'] = '鉴定意见通知书';
                } elseif ($value['class'] == 6) {
                    $value['class'] = '不予行政处罚决定书';
                } elseif ($value['class'] == 7) {
                    $value['class'] = '行政处罚决定书';
                } elseif ($value['class'] == 8) {
                    $value['class'] = '责令限期改正通知书';
                } elseif ($value['class'] == 9) {
                    $value['class'] = '没收违法所得、非法财物清单';
                } elseif ($value['class'] == 10) {
                    $value['class'] = '收缴物品清单';
                } elseif ($value['class'] == 11) {
                    $value['class'] = '追缴物品清单';
                } elseif ($value['class'] == 12) {
                    $value['class'] = '催告书';
                } elseif ($value['class'] == 13) {
                    $value['class'] = '终止案件调查决定书';
                } else {
                    $value['class'] = '未知';
                }
                if ($value['status']==0){
                    $value['status']='未签收';
                }elseif ($value['status']==1){
                    $value['status']='已签收';
                }else{
                    $value['status']='未知';
                }
                if ($value['uploadwrit']==0){
                    $value['uploadwrit']='未上传';
                }elseif ($value['uploadwrit']==1){
                    $value['uploadwrit']='已上传';
                }else{
                    $value['uploadwrit']='未知';
                }
                $sheet->setCellValue('A' . $row, $row-2);
                $sheet->setCellValue('B' . $row, $value['casenumber']);
                $sheet->setCellValue('C' . $row, $value['title']);
                $sheet->setCellValue('D' . $row, $value['receive']);
                $sheet->setCellValue('E' . $row, $value['contact']);
                $sheet->setCellValue('F' . $row, $value['class']);
                $sheet->setCellValue('G' . $row, $value['text']);
                $sheet->setCellValue('H' . $row, $value['uploadwrit']);
                $sheet->setCellValue('I' . $row, $value['reach_time']);
                $sheet->setCellValue('J' . $row, $value['receive_time']);
                $sheet->setCellValue('K' . $row, $value['status']);
                $row++;
            }
        }
        // 导出 Excel 文件
        // 检查并创建目录
        $dir = 'download/excel/' . $type . '/';
        if (!is_dir($dir)) {
            mkdir($dir, 0777, true); // 递归创建目录，并设置权限
        }
        $path = $dir . round(microtime(true) * 1000) . '.xlsx';
        $writer = IOFactory::createWriter($spreadsheet, 'Xlsx');
        $writer->save($path);
        if ($writer){
            return $path;
        }else{
            return false;
        }



    }
}