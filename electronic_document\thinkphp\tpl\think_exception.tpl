<?php
    if(!function_exists('parse_padding')){
        function parse_padding($source)
        {
            $length  = strlen(strval(count($source['source']) + $source['first']));
            return 40 + ($length - 1) * 8;
        }
    }

    if(!function_exists('parse_class')){
        function parse_class($name)
        {
            $names = explode('\\', $name);
            return '<abbr title="'.$name.'">'.end($names).'</abbr>';
        }
    }

    if(!function_exists('parse_file')){
        function parse_file($file, $line)
        {
            return '<a class="toggle" title="'."{$file} line {$line}".'">'.basename($file)." line {$line}".'</a>';
        }
    }

    if(!function_exists('parse_args')){
        function parse_args($args)
        {
            $result = [];

            foreach ($args as $key => $item) {
                switch (true) {
                    case is_object($item):
                        $value = sprintf('<em>object</em>(%s)', parse_class(get_class($item)));
                        break;
                    case is_array($item):
                        if(count($item) > 3){
                            $value = sprintf('[%s, ...]', parse_args(array_slice($item, 0, 3)));
                        } else {
                            $value = sprintf('[%s]', parse_args($item));
                        }
                        break;
                    case is_string($item):
                        if(strlen($item) > 20){
                            $value = sprintf(
                                '\'<a class="toggle" title="%s">%s...</a>\'',
                                htmlentities($item),
                                htmlentities(substr($item, 0, 20))
                            );
                        } else {
                            $value = sprintf("'%s'", htmlentities($item));
                        }
                        break;
                    case is_int($item):
                    case is_float($item):
                        $value = $item;
                        break;
                    case is_null($item):
                        $value = '<em>null</em>';
                        break;
                    case is_bool($item):
                        $value = '<em>' . ($item ? 'true' : 'false') . '</em>';
                        break;
                    case is_resource($item):
                        $value = '<em>resource</em>';
                        break;
                    default:
                        $value = htmlentities(str_replace("\n", '', var_export(strval($item), true)));
                        break;
                }

                $result[] = is_int($key) ? $value : "'{$key}' => {$value}";
            }

            return implode(', ', $result);
        }
    }
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>系统发生错误</title>
    <meta name="robots" content="noindex,nofollow" />
    <style>
        /* Base */
        body {
            color: #333;
            font: 16px Verdana, "Helvetica Neue", helvetica, Arial, 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 0 20px 20px;
        }
        h1{
            margin: 10px 0 0;
            font-size: 28px;
            font-weight: 500;
            line-height: 32px;
        }
        h2{
            color: #4288ce;
            font-weight: 400;
            padding: 6px 0;
            margin: 6px 0 0;
            font-size: 18px;
            border-bottom: 1px solid #eee;
        }
        h3{
            margin: 12px;
            font-size: 16px;
            font-weight: bold;
        }
        abbr{
            cursor: help;
            text-decoration: underline;
            text-decoration-style: dotted;
        }
        a{
            color: #868686;
            cursor: pointer;
        }
        a:hover{
            text-decoration: underline;
        }
        .line-error{
            background: #f8cbcb;
        }

        .echo table {
            width: 100%;
        }

        .echo pre {
            padding: 16px;
            overflow: auto;
            font-size: 85%;
            line-height: 1.45;
            background-color: #f7f7f7;
            border: 0;
            border-radius: 3px;
            font-family: Consolas, "Liberation Mono", Menlo, Courier, monospace;
        }

        .echo pre > pre {
            padding: 0;
            margin: 0;
        }
    
        /* Exception Info */
        .exception {
            margin-top: 20px;
        }
        .exception .message{
            padding: 12px;
            border: 1px solid #ddd;
            border-bottom: 0 none;
            line-height: 18px;
            font-size:16px;
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
            font-family: Consolas,"Liberation Mono",Courier,Verdana,"微软雅黑";
        }

        .exception .code{
            float: left;
            text-align: center;
            color: #fff;
            margin-right: 12px;
            padding: 16px;
            border-radius: 4px;
            background: #999;
        }
        .exception .source-code{
            padding: 6px;
            border: 1px solid #ddd;

            background: #f9f9f9;
            overflow-x: auto;

        }
        .exception .source-code pre{
            margin: 0;
        }
        .exception .source-code pre ol{
            margin: 0;
            color: #4288ce;
            display: inline-block;
            min-width: 100%;
            box-sizing: border-box;
        font-size:14px;
            font-family: "Century Gothic",Consolas,"Liberation Mono",Courier,Verdana;
            padding-left: <?php echo (isset($source) && !empty($source)) ? parse_padding($source) : 40;  ?>px;
        }
        .exception .source-code pre li{
            border-left: 1px solid #ddd;
            height: 18px;
            line-height: 18px;
        }
        .exception .source-code pre code{
            color: #333;
            height: 100%;
            display: inline-block;
            border-left: 1px solid #fff;
        font-size:14px;
            font-family: Consolas,"Liberation Mono",Courier,Verdana,"微软雅黑";
        }
        .exception .trace{
            padding: 6px;
            border: 1px solid #ddd;
            border-top: 0 none;
            line-height: 16px;
        font-size:14px;
            font-family: Consolas,"Liberation Mono",Courier,Verdana,"微软雅黑";
        }
        .exception .trace ol{
            margin: 12px;
        }
        .exception .trace ol li{
            padding: 2px 4px;
        }
        .exception div:last-child{
            border-bottom-left-radius: 4px;
            border-bottom-right-radius: 4px;
        }

        /* Exception Variables */
        .exception-var table{
            width: 100%;
            margin: 12px 0;
            box-sizing: border-box;
            table-layout:fixed;
            word-wrap:break-word;            
        }
        .exception-var table caption{
            text-align: left;
            font-size: 16px;
            font-weight: bold;
            padding: 6px 0;
        }
        .exception-var table caption small{
            font-weight: 300;
            display: inline-block;
            margin-left: 10px;
            color: #ccc;
        }
        .exception-var table tbody{
            font-size: 13px;
            font-family: Consolas,"Liberation Mono",Courier,"微软雅黑";
        }
        .exception-var table td{
            padding: 0 6px;
            vertical-align: top;
            word-break: break-all;
        }
        .exception-var table td:first-child{
            width: 28%;
            font-weight: bold;
            white-space: nowrap;
        }
        .exception-var table td pre{
            margin: 0;
        }

        /* Copyright Info */
        .copyright{
            margin-top: 24px;
            padding: 12px 0;
            border-top: 1px solid #eee;
        }

        /* SPAN elements with the classes below are added by prettyprint. */
        pre.prettyprint .pln { color: #000 }  /* plain text */
        pre.prettyprint .str { color: #080 }  /* string content */
        pre.prettyprint .kwd { color: #008 }  /* a keyword */
        pre.prettyprint .com { color: #800 }  /* a comment */
        pre.prettyprint .typ { color: #606 }  /* a type name */
        pre.prettyprint .lit { color: #066 }  /* a literal value */
        /* punctuation, lisp open bracket, lisp close bracket */
        pre.prettyprint .pun, pre.prettyprint .opn, pre.prettyprint .clo { color: #660 }
        pre.prettyprint .tag { color: #008 }  /* a markup tag name */
        pre.prettyprint .atn { color: #606 }  /* a markup attribute name */
        pre.prettyprint .atv { color: #080 }  /* a markup attribute value */
        pre.prettyprint .dec, pre.prettyprint .var { color: #606 }  /* a declaration; a variable name */
        pre.prettyprint .fun { color: red }  /* a function name */
    </style>
</head>
<body>
    <div class="echo">
        <?php echo $echo;?>
    </div>
    <?php if(\think\facade\App::isDebug()) { ?>
    <div class="exception">
    <div class="message">
        
            <div class="info">
                <div>
                    <h2>[<?php echo $code; ?>]&nbsp;<?php echo sprintf('%s in %s', parse_class($name), parse_file($file, $line)); ?></h2>
                </div>
                <div><h1><?php echo nl2br(htmlentities($message)); ?></h1></div>
            </div>
        
    </div>
	<?php if(!empty($source)){?>
        <div class="source-code">
            <pre class="prettyprint lang-php"><ol start="<?php echo $source['first']; ?>"><?php foreach ((array) $source['source'] as $key => $value) { ?><li class="line-<?php echo $key + $source['first']; ?>"><code><?php echo htmlentities($value); ?></code></li><?php } ?></ol></pre>
        </div>
	<?php }?>
        <div class="trace">
            <h2>Call Stack</h2>
            <ol>
                <li><?php echo sprintf('in %s', parse_file($file, $line)); ?></li>
                <?php foreach ((array) $trace as $value) { ?>
                <li>
                <?php 
                    // Show Function
                    if($value['function']){
                        echo sprintf(
                            'at %s%s%s(%s)', 
                            isset($value['class']) ? parse_class($value['class']) : '',
                            isset($value['type'])  ? $value['type'] : '', 
                            $value['function'], 
                            isset($value['args'])?parse_args($value['args']):''
                        );
                    }

                    // Show line
                    if (isset($value['file']) && isset($value['line'])) {
                        echo sprintf(' in %s', parse_file($value['file'], $value['line']));
                    }
                ?>
                </li>
                <?php } ?>
            </ol>
        </div>
    </div>
    <?php } else { ?>
    <div class="exception">
        
            <div class="info"><h1><?php echo htmlentities($message); ?></h1></div>
        
    </div>
    <?php } ?>
    
    <?php if(!empty($datas)){ ?>
    <div class="exception-var">
        <h2>Exception Datas</h2>
        <?php foreach ((array) $datas as $label => $value) { ?>
        <table>
            <?php if(empty($value)){ ?>
            <caption><?php echo $label; ?><small>empty</small></caption>
            <?php } else { ?>
            <caption><?php echo $label; ?></caption>
            <tbody>
                <?php foreach ((array) $value as $key => $val) { ?>
                <tr>
                    <td><?php echo htmlentities($key); ?></td>
                    <td>
                        <?php 
                            if(is_array($val) || is_object($val)){ 
                                echo htmlentities(json_encode($val, JSON_PRETTY_PRINT));
                            } else if(is_bool($val)) { 
                                echo $val ? 'true' : 'false';
                            } else if(is_scalar($val)) {
                                echo htmlentities($val);
                            } else {
                                echo 'Resource';
                            }
                        ?>
                    </td>
                </tr>
                <?php } ?>
            </tbody>
            <?php } ?>
        </table>
        <?php } ?>
    </div>
    <?php } ?>

    <?php if(!empty($tables)){ ?>
    <div class="exception-var">
        <h2>Environment Variables</h2>
        <?php foreach ((array) $tables as $label => $value) { ?>
        <table>
            <?php if(empty($value)){ ?>
            <caption><?php echo $label; ?><small>empty</small></caption>
            <?php } else { ?>
            <caption><?php echo $label; ?></caption>
            <tbody>
                <?php foreach ((array) $value as $key => $val) { ?>
                <tr>
                    <td><?php echo htmlentities($key); ?></td>
                    <td>
                        <?php 
                            if(is_array($val) || is_object($val)){ 
                                echo htmlentities(json_encode($val, JSON_PRETTY_PRINT));
                            } else if(is_bool($val)) { 
                                echo $val ? 'true' : 'false';
                            } else if(is_scalar($val)) {
                                echo htmlentities($val);
                            } else {
                                echo 'Resource';
                            }
                        ?>
                    </td>
                </tr>
                <?php } ?>
            </tbody>
            <?php } ?>
        </table>
        <?php } ?>
    </div>
    <?php } ?>

    <div class="copyright">
        <svg width="100%" height="100%" viewBox="0 0 400 300" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M295 89.7H100v115.799h195v-115.8Z" stroke="#0C296E" stroke-width=".5" stroke-miterlimit="10"/><path d="M295 83.6v6.1H100v-6.1c0-8.1 6.6-14.7 14.8-14.7h165.5c8.1-.1 14.7 6.5 14.7 14.7Z" stroke="#0C296E" stroke-width=".5" stroke-miterlimit="10"/><path d="M250.5 82a2.7 2.7 0 1 0 0-5.4 2.7 2.7 0 0 0 0 5.4Z" fill="#FFC60A"/><path d="M262.4 82a2.7 2.7 0 1 0 0-5.4 2.7 2.7 0 0 0 0 5.4Z" fill="#00D6B9"/><path d="M274.3 82a2.7 2.7 0 1 0 0-5.4 2.7 2.7 0 0 0 0 5.4Z" fill="#FFC60A"/><path d="M164.3 148.3h-8.9v15.9h-5.5v-15.9h-20.1v-3.5l19.2-27.9h6.4V144h8.9v4.3Zm-14.4-4.3v-13.3c0-2.1.3-5.6.4-7.9h-.3c-1.1 2.1-2.4 4.3-3.7 6.4L135.8 144h14.1ZM264.1 148.3h-8.9v15.9h-5.5v-15.9h-20.1v-3.5l19.2-27.9h6.4V144h8.9v4.3Zm-14.4-4.3v-13.3c0-2.1.3-5.6.4-7.9h-.3c-1.1 2.1-2.4 4.3-3.7 6.4L235.6 144h14.1Z" fill="#00D6B9" stroke="#00D6B9" stroke-width="1.408" stroke-miterlimit="10"/><path d="m251 173.2-29 13.7-33.9 5.9-10.2-9.7 5.2-44 39-9.9 28.9 44ZM217.5 108.9c-4.5-6.2-13.4-9.2-22.5-7-9.1 2.2-15.6 9-16.8 16.5 5.7-2.2 12.3-4.2 19.3-5.9 7.2-1.8 13.9-3 20-3.6Z" fill="#fff" stroke="#0C296E" stroke-width=".5" stroke-miterlimit="10"/><path d="M178.3 118.3c-14.4 5.5-23.6 11.9-22.5 16.4 1.5 6.4 22.8 6.7 47.5.8s43.4-15.9 41.9-22.3c-1.1-4.6-12.3-6-27.5-4.3" stroke="#0C296E" stroke-width=".5" stroke-miterlimit="10"/><path d="M223.4 123.8c.3-1.3-.4-2.6-1.7-2.9-4-1-12.4-.7-21.5 1.5-10.4 2.5-16.1 5.5-19.9 9.1-.8.8-.9 2.2-.2 3.1l3.1 4 38.9-9.4 1.1-4.4c0-.4.1-.7.2-1Z" fill="#fff" stroke="#0C296E" stroke-width=".5" stroke-miterlimit="10"/><path d="M203.779 138.968c10.684-2.576 18.809-6.885 18.149-9.623-.66-2.738-9.856-2.869-20.54-.293s-18.81 6.884-18.149 9.623c.66 2.738 9.856 2.869 20.54.293Z" fill="#0C296E" stroke="#0C296E" stroke-width=".455" stroke-miterlimit="10"/><path d="M172.065 131.343c1.449-.349 2.415-1.503 2.156-2.577-.259-1.073-1.644-1.661-3.094-1.311-1.449.35-2.415 1.503-2.156 2.577.259 1.074 1.644 1.661 3.094 1.311Z" fill="#FFC60A"/><path d="M185.742 124.039c.804-.675.793-2.01-.023-2.984-.817-.973-2.13-1.215-2.934-.54-.804.675-.794 2.01.023 2.983.816.973 2.13 1.215 2.934.541Z" fill="#00D6B9"/><path d="M199.671 120.043c1.181-.285 1.876-1.604 1.552-2.946-.323-1.342-1.543-2.199-2.724-1.914-1.182.284-1.877 1.603-1.553 2.945.324 1.343 1.543 2.2 2.725 1.915Z" fill="#FFC60A"/><path d="M214.939 117.438c1.074-.258 1.693-1.513 1.382-2.801-.311-1.289-1.433-2.124-2.507-1.865-1.073.259-1.692 1.514-1.381 2.802.31 1.289 1.433 2.123 2.506 1.864Z" fill="#00D6B9"/><path d="M229.888 117.652c1.503-.362 2.502-1.57 2.23-2.698-.272-1.127-1.711-1.747-3.214-1.385-1.504.363-2.502 1.571-2.23 2.698.272 1.127 1.711 1.748 3.214 1.385Z" fill="#FFC60A"/><path d="m191.2 170.301-.5 11.7M221 170.7l4.8 13.8M220.5 140.9l6.3 11M226.2 145.7l4.9 7.6M201.8 149.7l.7 10.5" stroke="#0C296E" stroke-width=".5" stroke-miterlimit="10"/><path d="M199.2 104.1s5-1.5 9.5 1.9" stroke="#0C296E" stroke-width=".939" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/><path d="M210.6 108.6a.8.8 0 1 0 0-1.6.8.8 0 0 0 0 1.6ZM195 128.7a1 1 0 1 0 0-2 1 1 0 0 0 0 2ZM207.1 125.8a1 1 0 1 0 0-2 1 1 0 0 0 0 2Z" fill="#0C296E"/><path d="m199.2 127.5.2-.1c1.1-1 2.6-1.3 4-.9" stroke="#0C296E" stroke-width=".49" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/><path d="M132 172.699s32.4-27.7 55.8 20.2l-55.8-20.2Z" fill="#fff"/><path d="M132 172.699s32.4-27.7 55.8 20.2" stroke="#0C296E" stroke-width=".5" stroke-miterlimit="10"/><path d="M185.8 195s20.9-23.2 50-.2l-50 .2Z" fill="#fff"/><path d="M185.8 195s20.9-23.2 50-.2" stroke="#0C296E" stroke-width=".5" stroke-miterlimit="10"/><path d="M235.7 188.399s-2.8-25.3 30-19.5l-30 19.5Z" fill="#fff"/><path d="M235.7 188.399s-2.8-25.3 30-19.5" stroke="#0C296E" stroke-width=".5" stroke-miterlimit="10"/><path d="M265.9 116.3v1.9M265.9 120.2v1.8M263 119.2h1.9M266.9 119.2h1.9" stroke="#0C296E" stroke-width=".5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/><path d="M162.7 107.6a1.4 1.4 0 1 0 0-2.8 1.4 1.4 0 0 0 0 2.8ZM260.3 181.8a1.1 1.1 0 1 0 0-2.2 1.1 1.1 0 0 0 0 2.2ZM127.5 162.1a1.3 1.3 0 1 0 0-2.6 1.3 1.3 0 0 0 0 2.6ZM133.1 120.1l-2.3-1.8 2.7-1.1-.4 2.9ZM253 98.3l-1.6 2.7h3.1l-1.5-2.7Z" fill="#fff" stroke="#0C296E" stroke-width=".5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/><path d="M122.353 244.705v-1.211l5.344-8.457h.879v1.877h-.594l-4.038 6.39v.095h7.198v1.306h-8.789Zm5.724 2.494v-12.162h1.402v12.162h-1.402Zm9.257.166c-.895 0-1.657-.243-2.286-.73-.63-.491-1.111-1.201-1.443-2.132-.333-.934-.499-2.062-.499-3.385 0-1.314.166-2.436.499-3.367.336-.934.819-1.647 1.449-2.138.633-.494 1.393-.742 2.28-.742.887 0 1.645.248 2.274.742.634.491 1.117 1.204 1.449 2.138.337.931.505 2.053.505 3.367 0 1.323-.166 2.451-.499 3.385-.332.931-.813 1.641-1.443 2.132-.629.487-1.391.73-2.286.73Zm0-1.306c.887 0 1.576-.428 2.066-1.283.491-.855.737-2.074.737-3.658 0-1.053-.113-1.949-.339-2.69-.221-.74-.542-1.304-.962-1.692a2.121 2.121 0 0 0-1.502-.582c-.879 0-1.566.433-2.061 1.3-.495.863-.742 2.085-.742 3.664 0 1.053.111 1.948.332 2.684.222.737.541 1.297.957 1.681.419.384.924.576 1.514.576Zm6.2-1.354v-1.211l5.344-8.457h.879v1.877h-.594l-4.038 6.39v.095h7.197v1.306h-8.788Zm5.724 2.494v-12.162h1.402v12.162h-1.402Zm12.392-9.097c.669 0 1.271-.485 1.271-1.254s-.602-1.254-1.271-1.254c-.669 0-1.254.485-1.254 1.254s.585 1.254 1.254 1.254Zm0 8.194c.669 0 1.271-.501 1.271-1.254 0-.769-.602-1.271-1.271-1.271-.669 0-1.254.502-1.254 1.271 0 .753.585 1.254 1.254 1.254Zm24.064-5.033v-1.221h-6.723v-2.876h5.569v-1.221h-5.569v-2.726h-1.287v2.726h-5.402v1.221h5.402v2.876h-6.606v1.221h5.903c-1.505 2.157-4.03 4.231-6.354 5.267.284.234.685.719.903 1.02 2.19-1.103 4.532-3.093 6.154-5.317v6.254h1.287v-6.321c1.622 2.224 3.997 4.297 6.204 5.384.218-.317.619-.802.903-1.036-2.324-1.02-4.866-3.111-6.388-5.251h6.004Zm15.535-5.268c-.468-.669-1.488-1.689-2.308-2.374l-.919.618c.819.719 1.789 1.773 2.24 2.475l.987-.719Zm-7.876 5.535-.151-1.137c-.702.184-1.405.402-2.073.585v-3.244h2.04v-1.17h-2.04v-3.345h-1.204v3.345h-2.392v1.17h2.392v3.579c-.97.267-1.873.518-2.592.686l.367 1.204c.669-.201 1.422-.402 2.225-.653v4.348c0 .234-.101.318-.335.318-.201 0-.92.017-1.722-.017.167.335.334.836.384 1.154 1.154 0 1.84-.033 2.275-.234.434-.184.602-.518.602-1.221v-4.716l2.224-.652Zm7.174-2.09a14.065 14.065 0 0 1-2.375 3.645c-.351-1.237-.652-2.725-.87-4.398l5.118-.551-.134-1.138-5.117.519a77.963 77.963 0 0 1-.301-4.248h-1.238c.067 1.539.184 2.994.318 4.382l-2.559.267.117 1.171 2.559-.268c.268 2.04.602 3.846 1.104 5.318-1.271 1.187-2.743 2.191-4.281 2.793.351.25.735.652.97.97 1.321-.602 2.625-1.489 3.779-2.542.803 1.839 1.923 2.926 3.411 3.043.836.067 1.489-.769 1.873-3.478-.267-.1-.802-.401-1.053-.686-.168 1.857-.435 2.76-.887 2.726-.953-.1-1.739-1.037-2.357-2.575 1.22-1.305 2.24-2.81 2.943-4.331l-1.02-.619Zm8.695 6.672v-2.776h3.144v-1.137h-3.144v-1.99h-1.204v1.99h-3.194v1.137h3.194v2.927c-1.471.184-2.809.351-3.863.468l.201 1.237c2.207-.317 5.468-.769 8.579-1.22l-.034-1.121-3.679.485Zm.385-9.314c.435.518.903 1.12 1.321 1.722-1.338.067-2.659.117-3.846.167.468-.986.953-2.224 1.371-3.277h4.415v-1.104h-8.529v1.104h2.726c-.318 1.037-.786 2.341-1.237 3.328l-1.522.067.1 1.187c1.907-.117 4.599-.251 7.224-.435.234.385.418.736.569 1.037l.97-.585c-.468-1.021-1.639-2.576-2.642-3.73l-.92.519Zm5.167-1.589h-1.187v9.198h1.187v-9.198Zm2.743-1.538v13.127c0 .318-.101.401-.402.418-.317 0-1.371.017-2.458-.034.167.368.351.954.418 1.288 1.355 0 2.341-.017 2.893-.217.552-.218.753-.602.753-1.455v-13.127h-1.204Zm8.896 2.408v1.237h9.682v-1.237h-9.682Zm3.11 3.695v1.188h4.516v-1.188h-4.516Zm4.114 0v.235c-.167 4.832-.401 6.638-.786 7.056-.15.184-.301.218-.619.201-.317 0-1.12 0-1.99-.084.201.335.335.837.368 1.205.853.05 1.689.066 2.158.016.501-.05.836-.167 1.153-.552.519-.602.736-2.408.954-7.491.016-.168.016-.586.016-.586h-1.254Zm-4.782-3.026c-.101 4.414-.251 8.695-3.044 10.953.318.201.719.585.92.886 2.976-2.525 3.277-7.057 3.378-11.839h-1.254Zm1.204-3.261c.301.819.652 1.923.802 2.575l1.238-.385c-.168-.635-.535-1.689-.853-2.491l-1.187.301Zm-7.81.702c.786.786 1.84 1.89 2.358 2.559l.92-.887c-.535-.635-1.606-1.672-2.392-2.424l-.886.752Zm1.087 13.596c.234-.352.669-.736 3.679-3.011a6.347 6.347 0 0 1-.368-1.003l-3.211 2.274-.334.535.234 1.205Zm-2.408-9.399v1.204h3.411v-1.204h-3.411Zm2.408 9.399c0-.485 1.422-1.539 1.422-1.539v-7.86h-1.255v6.773c0 .769-.602 1.355-.953 1.589.234.234.652.752.786 1.037Zm15.084-10.853v11.622h1.237v-11.622h-1.237Zm.184-2.96c.853.886 1.94 2.09 2.491 2.809l.954-.686c-.552-.702-1.689-1.889-2.526-2.709l-.919.586Zm4.214.134v1.17h8.829v-1.17h-8.829Zm7.976 0v12.675c0 .284-.1.385-.401.401-.267 0-1.288.017-2.291-.033.167.351.368.92.418 1.271 1.371.017 2.274-.017 2.81-.217.518-.218.719-.602.719-1.405v-12.692h-1.255Zm-7.909 4.13v1.137h4.03v3.88h-4.03v1.137h5.251v-6.154h-5.251Zm-.636 0v7.241h1.154v-7.241h-1.154Zm19.131 4.247c0 1.79-.703 3.746-6.873 5.001.267.25.619.735.752 1.003 6.455-1.405 7.375-3.713 7.375-5.987v-3.01H261.4v2.993Zm1.338 2.86c1.939.87 4.414 2.258 5.635 3.194l.769-.987c-1.271-.919-3.796-2.224-5.702-3.06l-.702.853Zm-7.827-11.221v1.171h6.238a17.788 17.788 0 0 1-.769 1.99h-3.83v7.759h1.238v-6.605h8.511v6.572h1.305v-7.726h-5.97a38.23 38.23 0 0 0 .936-1.99h6.639v-1.171h-14.298Zm29.114 12.325h-2.508v-7.693h2.508v7.693Zm-10.685-7.693h2.374v7.693h-2.374v-7.693Zm7.04 1.84h-3.529v-1.84h3.529v1.84Zm-3.529 3.913h3.529v1.94h-3.529v-1.94Zm3.529-1.02h-3.529v-1.873h3.529v1.873Zm5.702-7.977v-1.187h-14.699v1.187h6.405a25.602 25.602 0 0 1-.485 2.091h-5.167v10.886h1.204v-.886h10.685v.886h1.255v-10.886h-6.706c.217-.636.451-1.388.652-2.091h6.856Z" fill="#000"/></svg>
    </div>
    <?php if(\think\facade\App::isDebug()) { ?>
    <script>
        var LINE = <?php echo $line; ?>;

        function $(selector, node){
            var elements;

            node = node || document;
            if(document.querySelectorAll){
                elements = node.querySelectorAll(selector);
            } else {
                switch(selector.substr(0, 1)){
                    case '#':
                        elements = [node.getElementById(selector.substr(1))];
                        break;
                    case '.':
                        if(document.getElementsByClassName){
                            elements = node.getElementsByClassName(selector.substr(1));
                        } else {
                            elements = get_elements_by_class(selector.substr(1), node);
                        }
                        break;
                    default:
                        elements = node.getElementsByTagName();
                }
            }
            return elements;

            function get_elements_by_class(search_class, node, tag) {
                var elements = [], eles, 
                    pattern  = new RegExp('(^|\\s)' + search_class + '(\\s|$)');

                node = node || document;
                tag  = tag  || '*';

                eles = node.getElementsByTagName(tag);
                for(var i = 0; i < eles.length; i++) {
                    if(pattern.test(eles[i].className)) {
                        elements.push(eles[i])
                    }
                }

                return elements;
            }
        }

        $.getScript = function(src, func){
            var script = document.createElement('script');
            
            script.async  = 'async';
            script.src    = src;
            script.onload = func || function(){};
            
            $('head')[0].appendChild(script);
        }

        ;(function(){
            var files = $('.toggle');
            var ol    = $('ol', $('.prettyprint')[0]);
            var li    = $('li', ol[0]);   

            // 短路径和长路径变换
            for(var i = 0; i < files.length; i++){
                files[i].ondblclick = function(){
                    var title = this.title;

                    this.title = this.innerHTML;
                    this.innerHTML = title;
                }
            }

            // 设置出错行
            var err_line = $('.line-' + LINE, ol[0])[0];
            err_line.className = err_line.className + ' line-error';

            $.getScript('//cdn.bootcdn.net/ajax/libs/prettify/r298/prettify.min.js', function(){
                prettyPrint();

                // 解决Firefox浏览器一个很诡异的问题
                // 当代码高亮后，ol的行号莫名其妙的错位
                // 但是只要刷新li里面的html重新渲染就没有问题了
                if(window.navigator.userAgent.indexOf('Firefox') >= 0){
                    ol[0].innerHTML = ol[0].innerHTML;
                }
            });

        })();
    </script>
    <?php } ?>
</body>
</html>
