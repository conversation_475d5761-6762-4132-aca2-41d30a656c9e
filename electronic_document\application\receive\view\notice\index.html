<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
		<title>行政处罚决定书</title>
		<link rel="stylesheet" href="../../static/css/notice.css">
		<script src="../../static/js/vue2.js"></script>
		<!-- <link rel="stylesheet" href="../../static/css/<EMAIL>"> -->
		<script src="../../static/js/<EMAIL>"></script>
		<script src="../../static/js/axios.min.js"></script>
		<!-- <script src="https://cdn.jsdelivr.net/npm/vue@2/dist/vue.js"></script> -->
		<link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
		<!-- <script src="https://unpkg.com/element-ui/lib/index.js"></script> -->
	</head>
	<body>
		    <div id="app">  
		        <el-row justify="center"  class="row-class"  align="top"> 
				 <el-col :span="24">
				 <div id="text" style="white-space: pre-wrap;">{$text}</div>
				 </el-col>
				 <el-col :span="24">
					{foreach $imgs as $img}
					     <el-image
					       style="width: 100%;"
					       src="{$img}"
					       fit="fill"></el-image>
					{/foreach}
				</el-col>
				{if $status}
				<el-col :span="24">
					<div id="text" style="white-space: pre-wrap;">签署记录</div>
					<el-image
					style="width: 100%;border: 1px solid #000000;"
					src="{$signature}"
					fit="fill"></el-image>
					<div class="btn-audio clear">
                        <div class="lf">
                            <p style=" color: #333;margin: 1rem 0 0.3rem 1.2rem;">文书确认</p>
                            <p style=" color: #888; margin: 0 0 0 1.2rem">确认签署录音</p>
                        </div>
                        <div class="audio-container">
							<audio src="{$recorded}" controls="controls"></audio>
                        </div>
                    </div>
				</el-col>
				{/if}
				  <el-col :span="24">
				{if $step=='success'}
				<el-button id="sign" disabled  type="primary">已知悉</el-button>
				{else}
				<el-button id="sign" @click="tourl('{$step}')"  type="primary">已知悉</el-button>  
				{/if}
				</el-col>

			
		        </el-row>
	</body>
</html>
		<script src="../../static/js/notice.js"></script>