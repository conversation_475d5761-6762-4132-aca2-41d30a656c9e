<?php

namespace app\receive\controller;

use think\Db;

class Notice
{
    public function index($uid = null){
        if (is_null($uid)) {
            return json(['status' => 'error', 'message' => '参数异常']);
        }else{
            $text = Db::name('send')->where('is_delete', 0)->where('uid', $uid)->value('text');
            $imgs=Db::name('file')->where('class',1)->where('is_delete',0)->where('uid',$uid)->orderRaw("create_time ASC")->column('path');

        }
        if (!$text){
            return json(['status' => 'error', 'message' => '参数异常']);
        }
        //判断是否已经签署

        $status = Db::name('send')->where('is_delete', 0)->where('uid', $uid)->value('status');
        $signature = Db::name('file')->where('is_delete', 0)->where('uid', $uid)->where('class',2)->value('path');
        $recorded = Db::name('file')->where('is_delete', 0)->where('uid', $uid)->where('class',3)->value('path');

        //确定应该去哪部分进行签署
        $step='signature';
        if ($signature){
            $step='recorded';
        }

        if ($status&&$signature&&$recorded){
            $step='success';
        }
        return view('index',[
            'text'=>$text,
            'imgs'=>$imgs,
            'step'=>$step,
            'signature'=>$signature,
            'recorded'=>$recorded,
            'status'=>$status
        ]);
    }
}