<?php

namespace app\common;


class Validate extends \think\Validate
{
// 定义验证规则
    protected $rule = [
        'casenumber'  => 'require',
        'title'       => 'require',
        'receive'     => 'require',
        'contact'     => 'require|regex:/^1[3-9]\d{9}$/',
        'e_send'      => 'require|in:0,1', // 假设 e_send 只能是 0 或 1
        'server'      => 'require',
    ];

    // 定义错误消息
    protected $message = [
        'casenumber.require' => '案件编号不能为空',
        'title.require'      => '标题不能为空',
        'receive.require'    => '收件人不能为空',
        'contact.require'    => '收件人联系方式不能为空',
        'contact.regex'    => '收件人联系方式应是11位手机号码',
        'e_send.require'     => '电子送达不能为空',
        'e_send.in'          => '电子送达有误',
        'server.require'     => '送达人不能为空',


    ];
}
