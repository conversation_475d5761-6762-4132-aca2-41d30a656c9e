<?php

namespace app\common\service;

use GuzzleHttp\Client;
use InvalidArgumentException;
use RuntimeException;
use think\facade\Log;

class SmsService
{
    protected $appId;
    protected $appSecret;
    protected $apiUrl;

    public function __construct()
    {
        $this->param1 = [
            'apiKey' => 'sendwenshu',
            'apiSecrect' => 'c9@P3Y$G',
            'reqTime' => (string)round(microtime(true) * 1000),
        ];
        $this->param2=[
            'mmsId'=>1245646,//模板id
            'messageId'=>(string)round(microtime(true) * 1000),//唯一id用时间戳
        ];
        $this->apiUrl = 'http://36.138.133.214:30080/';
        $this->Token='';//初始化一个token
        $tokenResponse = $this->client('api/user/token',$this->param1);
        // 检查响应是否有效且包含token
        if (is_array($tokenResponse) && isset($tokenResponse['token'])) {
            $this->Token = $tokenResponse['token'];
        } else {
            Log::error('获取短信Token失败: 响应格式不正确. 响应内容: ' . var_export($tokenResponse, true));
            // 可以选择抛出异常或设置默认值
            throw new RuntimeException('获取短信Token失败: 响应格式不正确');
        }

    }

    //按照移动规则生成签名sign
    public static function generateSign(array $params)
    {
        // 去除 null 值
        $filteredParams = array_filter($params, function($value) {
            return $value !== null;
        });
        // 字典排序
        ksort($filteredParams);
        // 手动拼接字符串，不进行 URL 转义
        $stringToBeSigned = '';
        $first = true;
        foreach ($filteredParams as $key => $value) {
            if (!$first) {
                $stringToBeSigned .= '&';
            } else {
                $first = false;
            }
            // 注意：这里没有对 $key 和 $value 进行 urlencode，因此它们将不会被转义
            $stringToBeSigned .= "$key=$value";
        }
        // 检查输入是否为字符串
        if (!is_string($stringToBeSigned)) {
            throw new InvalidArgumentException('Input must be a string');
        }
        // 使用 SHA-256 算法进行哈希运算
        $hash = hash('sha256', $stringToBeSigned, false);
        // 检查哈希是否成功生成
        if ($hash === false) {
            throw new RuntimeException('Failed to generate SHA-256 hash');
        }
        // 将哈希值进行 Base64 编码
        $base64EncodedHash = base64_encode($hash);
        // 返回编码后的哈希值
        return $base64EncodedHash;
    }

    //封装一个发送post请求的方法
    public function client($url,$params){
        $sign = self::generateSign($params);
        $params['sign']=$sign;
        // 准备请求头
        $headers = [
            'Content-Type' => 'application/json',
        ];
        // 只有在不是获取token的请求时，才添加Authorization头
        if ($url !== 'api/user/token') {
            $headers['Authorization'] = $this->Token;
        }
        // 发送HTTP请求
        $client = new Client();
        try {
            $response = $client->post($this->apiUrl.$url, [
                'json' => $params,
                'headers' => $headers,
            ]);
        } catch (\Exception $e) {
            Log::error('HTTP请求异常: ' . $e->getMessage());
            throw new RuntimeException('发送HTTP请求失败: ' . $e->getMessage());
        }
        $statusCode = $response->getStatusCode();
        $body = $response->getBody()->getContents();
        // 处理响应
        Log::info('SMS Send Response: ' . $body);
        $result=json_decode($body,true);//解码为关联数组
        return $result;
    }

public function sendsms($num,$data)
    {
        //构造函数已经获取了token，直接执行单短信发送即可
        $this->param2['mobile']=$num;
        $this->param2['variableText']='receive='.$data['receive'].',time='.$data['time'].',class='.$data['type'].',addr='.$data['uid'];
        $res=$this->client('api/send/mms',$this->param2);
        return $res;
    }
}