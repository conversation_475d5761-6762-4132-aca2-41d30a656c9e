{include file="public/_head"}
<link rel="stylesheet" href="../../static/css/recorded.css">
<script src="../../static/js/js-audio-recorder.js"></script>
	<div id="app">
        <div class="basic">
            <h3 class="title">录音确认<span>（请本人点击录制并清晰朗读下方内容）</span></h3>
            <p>我是{$data.receive}，手机号码是{$data.contact}，我已与{$data.time}收到电子文书，并已阅读，现在完成签署。</p>
        </div>
        <div class="basic" style="text-align: center;">
            <p>{{text}}:{{ recordingTime }} 秒</p>
            <audio :src="audioUrl" controls>
        </div>
        <div class="basic" style="text-align: center;">

        <el-row justify="center" class="row-class1" align="top">
            <el-col class="button1" :span="7">
                <?xml version="1.0" encoding="UTF-8"?><svg @click="playRecording" id="play" width="100%" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M15 24V11.8756L25.5 17.9378L36 24L25.5 30.0622L15 36.1244V24Z" fill="none" stroke="#ffffff" stroke-width="4" stroke-linejoin="round"/></svg>
            </el-col>
            <el-col  class="button2" :span="10">
                <?xml version="1.0" encoding="UTF-8"?>
                <svg v-if="!ing" @click="startRecording" id="start" width="100%" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><rect x="17" y="4" width="14" height="27" rx="7" fill="none" stroke="#ffffff" stroke-width="4" stroke-linejoin="round"/><path d="M9 23C9 31.2843 15.7157 38 24 38C32.2843 38 39 31.2843 39 23" stroke="#ffffff" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/><path d="M24 38V44" stroke="#ffffff" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/></svg>
                <svg v-if="ing" @click="stopRecording" id="ing" width="100%" viewBox="0 0 320 320">
                 <!-- Define the wave path -->
                <!-- Define the wave path, adjusted to fit within the square viewBox -->
                <defs>
                    <path id="wavePath" d="M0,160 Q80,60 160,160 T320,160" />
                </defs>
                
                <!-- Use the wave path and animate its position -->
                <use xlink:href="#wavePath" class="wave">
                    <animateTransform attributeName="transform"
                                    attributeType="XML"
                                    type="translate"
                                    from="-320,0"
                                    to="0,0"
                                    dur="4s"
                                    repeatCount="indefinite"
                                    additive="sum" />
                </use>
                
                <!-- Duplicate the wave path for a continuous loop effect -->
                <use xlink:href="#wavePath" class="wave" transform="translate(320,0)">
                    <animateTransform attributeName="transform"
                                    attributeType="XML"
                                    type="translate"
                                    from="0,0"
                                    to="-320,0"
                                    dur="4s"
                                    repeatCount="indefinite"
                                    additive="sum" />
                </use>
            </svg>
            </el-col>
            <el-col class="button3" :span="7">
                <el-popconfirm
                title="是否确定上传？"
               @confirm="uploadRecording"
              >
              <?xml version="1.0" encoding="UTF-8"?><svg slot="reference" id="upload" width="100%" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M43 11L16.875 37L5 25.1818" stroke="#ffffff" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/></svg>
              </el-popconfirm>
            </el-col>
        </el-row>
    </div>



    </div>
<script src="../../static/js/recorded.js"></script>
{include file="public/_footer"}