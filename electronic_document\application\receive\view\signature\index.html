<!DOCTYPE html>  
<html lang="en">  
<head>  
  <meta charset="UTF-8">  
  <link rel="icon" href="/favicon.ico">  
  <meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
   <link rel="stylesheet" href="../../static/css/signature.css">
  <script src="../../static/js/vue2.js"></script>
  <script src="../../static/js/smooth-signature.js"></script>
  <script src="../../static/js/axios.min.js"></script>
  <script src="../../static/js/<EMAIL>"></script>
  <!-- <link rel="stylesheet" href="../../static/css/<EMAIL>"> -->
		<link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">

  <title>签署——行政处罚决定书</title>  
</head>  
<body>
	{if $status}
	<script>
		alert('您已经完成签署！');
		window.history.back();
	</script>
	{/if}
  <div id="app"> 
  <el-row id="row">
	  <el-col :span="24" id="buttons">
	  	<el-button type="primary" @click="reload">刷新</el-button>
	  	<el-button type="primary" @click="handleClear">清屏</el-button>
	  	<el-button type="primary" @click="handleUndo">撤销</el-button>
	  	<el-button type="primary" @click="handlePreview">提交</el-button>
	  </el-col>
    <el-col :span="24" id="canvasbox"><canvas class="canvas" ref="canvas" /></el-col>

  </el-row>
  <el-dialog
  v-if="tipsvisibleComputed"
    title="提示"
    :visible.sync="tipsvisible"
    width="30%">
    <span>建议使用手机端进行签署</span>
    <span slot="footer" class="dialog-footer">
      <el-button @click="tipsvisible = false">取 消</el-button>
      <el-button type="primary" @click="tipsvisible = false">确 定</el-button>
    </span>
  </el-dialog>

  </div>  
  <script src="../../static/js/signature.js"></script>
</body>  
</html>