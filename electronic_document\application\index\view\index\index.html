{include file="public/_head"}
<link rel="stylesheet" href="../../static/css/index.css">
	<div id="app">
		<div id="search">
		<el-row>  
				<el-col :span="6" class="search">
								  <label class="search_label">案件编号</label>
								  <el-input  
									placeholder="请输入"  
									v-model="search.casenumber"  
									clearable  
									style="flex: 1;">  
								  </el-input>  
				</el-col>
				<el-col :span="6" class="search">
								  <label class="search_label">案件名称</label>
								  <el-input  
									placeholder="请输入"  
									v-model="search.title"  
									clearable  
									style="flex: 1;">  
								  </el-input>  
				</el-col>
				<el-col :span="6" class="search">
								  <label class="search_label">接收人员</label>
								  <el-input  
									placeholder="请输入"  
									v-model="search.receive"  
									clearable  
									style="flex: 1;">  
								  </el-input>  
				</el-col>
				<el-col :span="6" class="search">
								  <label class="search_label">联系方式</label>
								  <el-input  
									placeholder="请输入"  
									v-model="search.contact"  
									clearable  
									style="flex: 1;">  
								  </el-input>  
				</el-col>
			</el-row>  
			
			<el-row>
				<el-col :span="4" class="search">
					<label class="search_label">签收状态</label>
					<el-select style="width: 70%;" v-model="search.signed_status" placeholder="全部">
					  <el-option
						v-for="item in signed_status"
						:key="item.value"
						:label="item.label"
						:value="item.value">
					  </el-option>
					</el-select>  
  			</el-col>
			<el-col :span="8" class="search">
							  <label class="search_label">送达时间</label>
							  <div class="block">
							      <el-date-picker
							        v-model="search.time"
							        type="daterange"
							        align="right"
							        unlink-panels
							        range-separator="至"
							        start-placeholder="开始日期"
							        end-placeholder="结束日期"
							        :picker-options="pickerOptions">
							      </el-date-picker>
							    </div>
			</el-col>
			<el-col :span="6" class="search">
				<el-button type="primary" @click="select_case" >查询</el-button>
				<el-button @click="downloadVisible = true">导出</el-button>
				<el-button  @click="addcaseVisible = true" >新建案件</el-button>
			</el-col>
			</el-row>
		</div>
		<div class="interval"></div>
		<div id="list">
			  <el-table
				id="table"
				stripe
			    :data="paginatedTableData"
				style="width: 100%;height: 75vh;"


				:max-height="table_height"
				>
				<el-table-column
				  label="序号"
				  type="index"
				  align="center"
				  min-width="100"

				  >
				</el-table-column>
			    <el-table-column
			      prop="casenumber"
			      label="案件编号"
				  align="center"
				  min-width="200"

			      >
			    </el-table-column>
			    <el-table-column
			      prop="title"
			      label="案件名称"
				  align="center"
				  min-width="200"

			      >
			    </el-table-column>
			    <el-table-column
			      prop="receive"
			      label="接收人员"
				  align="center"
				  min-width="180"

			      >
			    </el-table-column>
			    <el-table-column
			      prop="send_count"
			      label="发送次数"
				  align="center"
				  min-width="100"

			    >
				<template slot-scope="scope" >
						 <el-tag type="success">{{scope.row.send_count}}次</el-tag>
				</template>
			    </el-table-column>
			    <el-table-column
			      prop="reach_time"
			      label="最近一次送达时间"
				  align="center"
				  min-width="200"

			      >
			    </el-table-column>
				<el-table-column
				  prop="status"
				  label="最近一次送达状态"
				  align="center"
				  min-width="150"

				  >
				  <template slot-scope="scope">
						<template v-if="scope.row.status == 0">  
						  <el-tag type="warning">未签收</el-tag>  
						</template>  
						<template v-else-if="scope.row.status == 1">  
						  <el-tag type="success">已签收</el-tag>  
						</template>
						  <template v-else-if="scope.row.status === null">
						    <el-tag type="success">未发送</el-tag>  
						  </template>  
						<template v-else>  
						  <el-tag type="danger">未知</el-tag>  
						</template>  
				  </template>
				</el-table-column>
				<el-table-column
				  prop="receive_time"
				  label="最近一次签收时间"
				  min-width="200"

				  align="center"
				  >
				</el-table-column>
				{if session('user.level') eq 0}
				<el-table-column
				prop="create_user"
				min-width="200"

				label="负责部门">
			  </el-table-column>
			  {/if}
			    <el-table-column
			      label="操作"
				  min-width="200"

				  align="center"
			      >
			      <template slot-scope="scope">
			        <el-button
			          @click.native.prevent="case_detail(scope.row)"
			          type="text"
			          size="small">
			          案件信息
			        </el-button>
					<el-button
					  @click="addsend_dialog(scope.row)"
					  type="text"
					  size="small">
					  新建送达
					</el-button>
					<el-button
					  @click="send_detail(scope.row)"
					  type="text"
					  size="small">
					  送达详情
					</el-button>
			      </template>
			    </el-table-column>
			  </el-table>
			  
			      <el-pagination
					id="pagination"
					background
			        @size-change="handleSizeChange"
			        @current-change="handleCurrentChange"
			        :current-page="currentPage"
			        :page-sizes="[10, 20, 30, 50]"
			        :page-size="pageSize"
			        layout="total, sizes, prev, pager, next, jumper"
			        :total="case_total">
			      </el-pagination>
		</div>
		
		<el-dialog
		title="导出案件"
		:close-on-click-modal="false"
		:visible.sync="downloadVisible"
		width="40%"
		center>
		<div style="text-align: center;width: 100%;">
		<label class="search_label">请选择创建时间</label>
			<el-date-picker
			  v-model="downloadcase_time"
			  type="daterange"
			  align="right"
			  unlink-panels
			  range-separator="至"
			  start-placeholder="开始日期"
			  end-placeholder="结束日期"
			  :picker-options="pickerOptions">
			</el-date-picker>
		</div>
		<span slot="footer" class="dialog-footer">  
			<el-button @click="downloadVisible = false">取 消</el-button>  
			<el-button type="primary" @click="download_case()" :loading="btnloading">确 定</el-button>  
		</span> 
				 
	  </el-dialog>
		
		
		<el-dialog
		  title="新建案件"
		  :close-on-click-modal="false"
		  :visible.sync="addcaseVisible"
		  width="70%"
		  center>
				  <el-form :hide-required-asterisk="true" ref="addcase" label-width="150px" :rules="rules"  :model="addcase" :label-position="labelPosition" style="padding-right: 3rem;">
					<el-form-item  label="案件编号" prop="casenumber" >  
					  <el-input  
						placeholder="请输入案件编号"  
						v-model="addcase.casenumber"  
						clearable  
						>  
					  </el-input>  
					</el-form-item>  
					<el-form-item label="案件名称" prop="title" >  
					  <el-input  
						placeholder="请输入案件名称"  
						v-model="addcase.title"  
						clearable  
						>  
					  </el-input>  
					</el-form-item>  
					  
					<el-form-item label="接收人姓名" prop="receive" >  
					  <el-input  
						placeholder="请输入接收人姓名"  
						v-model="addcase.receive"  
						clearable  
						>  
					  </el-input>  
					</el-form-item>  
					  
					<el-form-item label="接收人联系方式" prop="contact" >  
					  <el-input  
						placeholder="请输入接收人联系方式"  
						v-model="addcase.contact"  
						clearable  
						>  
					  </el-input>  
					</el-form-item>  
					  
					<el-form-item label="电子送达" prop="e_send" >  
					  <el-radio-group v-model="addcase.e_send">  
						<el-radio label="1">同意</el-radio>  
						<el-radio label="0">不同意</el-radio>  
					  </el-radio-group>  
					</el-form-item>  
					  
					<el-form-item label="备注" prop="remark" >  
					  <el-input  
						type="textarea"  
						:rows="4"  
						placeholder="请输入备注"  
						v-model="addcase.remark"  
						clearable  
						>  
					  </el-input>  
					</el-form-item>  
					<el-form-item label="送达人" prop="server" >
						<el-select
						style="width: 100%;"
						v-model="addcase.server"
						multiple
						filterable
						allow-create
						default-first-option
						placeholder="请输入送达人">
						<el-option
						  key=""
						  label="输入名字后点击回车"
						  value=""
						  disabled
						  >
						</el-option>
					  </el-select>
						</el-form-item>
					  
					<el-form-item label="上传音频" >  
					  <el-upload
					  list-type="picture-card"
						accept=".mp3,.m4a"
						multiple
						class="upload-demo"  
						ref="upload"  
						action="/index/index/addfile"  
						:on-remove="upload_delete_audio"  
						:on-success='upload_add_audio'
						:before-upload="beforeAudioUpload"
						:file-list="addcase.fileList"
						>  
						<i slot="default" class="el-icon-plus"></i>
						<div slot="file" slot-scope="{file}">
							<?xml version="1.0" encoding="UTF-8"?><svg width="100%" height="100%" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M24 3.99976V43.9998" stroke="#2F88FF" stroke-width="4" stroke-linecap="round"/><path d="M34 11.9998V35.9998" stroke="#2F88FF" stroke-width="4" stroke-linecap="round"/><path d="M4 17.9998V29.9998" stroke="#2F88FF" stroke-width="4" stroke-linecap="round"/><path d="M44 17.9998V29.9998" stroke="#2F88FF" stroke-width="4" stroke-linecap="round"/><path d="M14 11.9998V35.9998" stroke="#2F88FF" stroke-width="4" stroke-linecap="round"/></svg>
						<span class="el-upload-list__item-actions">
							<span
							class="el-upload-list__item-delete"
							@click="handleDownload(file)"
						  >
							<i class="el-icon-download"></i>
							</span>

							<span
							class="el-upload-list__item-delete"
							@click="upload_delete_audio(file)"
							>
							<i class="el-icon-delete"></i>
							</span>
						</div>
					  </el-upload>  
					</el-form-item>  
					</el-form>
				  <span slot="footer" class="dialog-footer">  
					<el-button @click="addcaseVisible = false">取 消</el-button>  
					<el-button type="primary" @click="submit_addcase()" :loading="btnloading">确 定</el-button>  
				  </span> 
				   
		</el-dialog>
		
		<el-dialog
		  title="案件信息"
		  :close-on-click-modal="false"
		  :visible.sync="casedetailVisible"
		  width="70%"
		  center>
				  <el-form :hide-required-asterisk="true" ref="casedetail" label-width="150px" :rules="rules"  :model="casedetail" :label-position="labelPosition" style="padding-right: 3rem;">
					<el-form-item  label="案件编号" prop="casenumber" >  
					  <el-input  
						placeholder="请输入案件编号"  
						readonly
						v-model="casedetail.casenumber"  
						clearable  
						>  
					  </el-input>  
					</el-form-item>  
					<el-form-item label="案件名称" prop="title" >  
					  <el-input  
						placeholder="请输入案件名称"  
						v-model="casedetail.title"  
						clearable  
						>  
					  </el-input>  
					</el-form-item>  
					  
					<el-form-item label="接收人姓名" prop="receive" >  
					  <el-input  
						placeholder="请输入接收人姓名"  
						v-model="casedetail.receive"  
						clearable  
						>  
					  </el-input>  
					</el-form-item>  
					  
					<el-form-item label="接收人联系方式" prop="contact" >  
					  <el-input  
						placeholder="请输入接收人联系方式"  
						v-model="casedetail.contact"  
						clearable  
						>  
					  </el-input>  
					</el-form-item>  
					  
					<el-form-item label="电子送达" prop="e_send" >  
					  <el-radio-group v-model="casedetail.e_send">  
						<el-radio label="1">同意</el-radio>  
						<el-radio label="0">不同意</el-radio>  
					  </el-radio-group>  
					</el-form-item>  
					  
					<el-form-item label="备注" prop="remark" >  
					  <el-input  
						type="textarea"  
						:rows="4"  
						placeholder="请输入备注"  
						v-model="casedetail.remark"  
						clearable  
						>  
					  </el-input>  
					</el-form-item>  
					<el-form-item label="送达人" prop="server" >
						<el-select
						style="width: 100%;"
						v-model="casedetail.server"
						multiple
						filterable
						allow-create
						default-first-option
						placeholder="请输入送达人">
						<el-option
						  key=""
						  label="输入名字后点击回车"
						  value=""
						  disabled
						  >
						</el-option>
					  </el-select>
						</el-form-item>
					<el-form-item label="上传音频" >  
					  <el-upload
					  list-type="picture-card"
						accept=".mp3,.m4a"
						multiple
						class="upload-demo"  
						ref="upload"  
						action="/index/index/addfile"  
						:on-remove="gai_upload_delete_audio"  
						:on-success='gai_upload_add_audio'
						:before-upload="beforeAudioUpload"
						:file-list="old_fileList"
						>  
						<i slot="default" class="el-icon-plus"></i>
						<div slot="file" slot-scope="{file}">
							<?xml version="1.0" encoding="UTF-8"?><svg width="100%" height="100%" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M24 3.99976V43.9998" stroke="#2F88FF" stroke-width="4" stroke-linecap="round"/><path d="M34 11.9998V35.9998" stroke="#2F88FF" stroke-width="4" stroke-linecap="round"/><path d="M4 17.9998V29.9998" stroke="#2F88FF" stroke-width="4" stroke-linecap="round"/><path d="M44 17.9998V29.9998" stroke="#2F88FF" stroke-width="4" stroke-linecap="round"/><path d="M14 11.9998V35.9998" stroke="#2F88FF" stroke-width="4" stroke-linecap="round"/></svg>
						<span class="el-upload-list__item-actions">
							<span
							class="el-upload-list__item-delete"
							@click="handleDownload(file)"
						  >
							<i class="el-icon-download"></i>
							</span>

							<span
							class="el-upload-list__item-delete"
							@click="gai_upload_delete_audio(file)"
							>
							<i class="el-icon-delete"></i>
							</span>
						</div>
					  </el-upload>  
					</el-form-item>  
					</el-form>
				  <span slot="footer" class="dialog-footer">  
					<el-button type="danger" @click="open('delete')">删 除</el-button>  
					<el-button type="primary" @click="open('xiugai')">修 改</el-button>  
					<el-button type="primary" @click="casedetailVisible=false">关 闭</el-button>
				  </span> 
				   
		</el-dialog>
		
		<el-dialog
		  title="送达详情"
		  :visible.sync="senddetailVisible"
		  width="80%"
		  center>
		  <el-row>
		  		<el-col :span="6" class="search">
		  			<label>案件编号：{{senddetail.casenumber}}</label>
				</el-col>
				<el-col :span="6" class="search">
					<label>案件名称：{{senddetail.title}}</label>
				</el-col>
				<el-col :span="6" class="search">
					<label>接收人姓名：{{senddetail.receive}}</label>
				</el-col>
				<el-col :span="6" class="search">
					<label>接收人联系方式：{{senddetail.contact}}</label>
				</el-col>
			</el-row>
			<el-row id="senddetail">
				<el-table
				stripe
				:data="senddetailData"
				style="width: 100%;height: 80rem;"
				:max-height="senddetail_height"
				>
				<el-table-column
				  label="序号"
				  min-width="50"
				  type="index"
				  align="center"
				  >
				</el-table-column>
				<el-table-column
				label="文书名称"
				prop="writ_title"
				align="center"
				min-width="100"

				>
			  </el-table-column>
			  <el-table-column
			  label="文书编号"
			  prop="writ_number"
			  align="center"

			  min-width="100"

			  >
			</el-table-column>
			<el-table-column
			label="如何救济途径"
			prop="writ_function"
			align="center"
			min-width="120"

			>
		  </el-table-column>
			    <el-table-column
			      prop="class"
			      label="文书类型"
				  min-width="80"

				  align="center"
			      >
				  <template slot-scope="scope" >
					{{ 
						scope.row.class == 1 ? '行政案件立案告知书' :
						scope.row.class == 2 ? '不予立案告知书' :
						scope.row.class == 3 ? '证据保全决定书' :
						scope.row.class == 4 ? '行政处罚告知笔录' :
						scope.row.class == 5 ? '鉴定意见通知书' :
						scope.row.class == 6 ? '不予行政处罚决定书' :
						scope.row.class == 7 ? '行政处罚决定书' :
						scope.row.class == 8 ? '责令限期改正通知书' :
						scope.row.class == 9 ? '没收违法所得、非法财物清单' :
						scope.row.class == 10 ? '收缴物品清单' :
						scope.row.class == 11 ? '追缴物品清单' :
						scope.row.class == 12 ? '催告书' :
						scope.row.class == 13 ? '终止案件调查决定书' :
						'未知' 
					  }}
				  </template>
			    </el-table-column>
				<el-table-column  
				  prop="text"  
				  label="发送内容"
				  min-width="100"

				  align="center"  
				>  
				  <template slot-scope="scope">  
					<el-tooltip  
					  class="item"  
					  effect="dark"  
					  :content="scope.row.text || '无内容'"
					  placement="top"  
					>  
					  <span>{{ (scope.row.text || '').slice(0, 20) }}...</span> <!-- 使用空字符串作为默认值 -->  
					</el-tooltip>  
				  </template>  
				</el-table-column>
				<el-table-column
				  prop="reach_time"
				  min-width="100"

				  label="送达时间"
				  align="center"
				  >
				</el-table-column>
				<el-table-column
				  prop="status"
				  min-width="100"
				  label="状态"
				  align="center"
				  >
				  <template slot-scope="scope">
						<template v-if="scope.row.status == 0">  
						  <el-tag type="warning">未签收</el-tag>  
						</template>  
						<template v-else-if="scope.row.status == 1">  
						  <el-tag type="success">已签收</el-tag>  
						</template>  
						<template v-else>  
						  <el-tag type="danger">未知</el-tag>  
						</template>  
				  </template>  
				</el-table-column>
				<el-table-column
				  prop="receive_time"
				  label="签收时间"
				  min-width="100"

				  align="center"
				  >
				</el-table-column>
				<el-table-column
				label="备注"
				prop="remark"
				min-width="100"

				align="center"
				>
			  </el-table-column>
				<el-table-column
				label="送达人"
				align="center"
				min-width="200"
				>
				<template slot-scope="scope">  
				<el-tag
				v-for="(item, index) in scope.row.server"
				:key="index"
				type="success"
			  >
				{{ item }}
			  </el-tag>
				</template>  
			</el-table-column>

			<el-table-column
			label="操作"
			min-width="50"

			align="center"
			>
			<template slot-scope="scope">
			  <el-button
				@click.native.prevent="preview_detail(scope.row)"
				type="text"
				size="small">
				详情
			  </el-button>
			</template>
		  </el-table-column>
			  </el-table>
			</el-row>
			<span slot="footer" class="dialog-footer"> 
				<el-button type="primary" @click="senddetailVisible=false">关 闭</el-button>  
			  </span> 
		</el-dialog>
		<el-dialog
		  title="新建电子送达"
		  :visible.sync="addsendVisible"
		  :close-on-click-modal="false"
		  width="70%"
		  center>
			  <el-form :hide-required-asterisk="true" ref="addsend" label-width="150px" :rules="rules"  :model="addsend" :label-position="labelPosition" style="padding-right: 3rem;">
					<el-form-item label="案件编号" prop="casenumber" >
						  <el-input  
							placeholder="请输入案件编号"  
							v-model="addsend.casenumber"  
							:readonly="true"
							clearable  
							>  
						  </el-input>  
					</el-form-item>
					<el-form-item label="案件名称" prop="title" >
					  <el-input  
						placeholder="请输入案件名称"  
						v-model="addsend.title"  
						:readonly="true"
						clearable  
						>  
					  </el-input> 
					   </el-form-item>
					   <el-form-item label="接收人姓名" prop="receive" >
					  <el-input  
						placeholder="请输入接收人姓名"  
						:readonly="true"
						v-model="addsend.receive"  
						clearable  
						>  
					  </el-input>  
					  </el-form-item>
					  <el-form-item label="接收人联系方式" prop="contact" >
					  <el-input  
						placeholder="请输入接收人联系方式"  
						:readonly="true"
						v-model="addsend.contact"  
						clearable  
						>  
					  </el-input>  
					  </el-form-item>
					  <el-form-item label="文书名称" prop="writ_title" >
						<el-input  
						  placeholder="请输入文书名称"  
						  v-model="addsend.writ_title"  
						  clearable  
						  >  
						</el-input>  
						</el-form-item>
						<el-form-item label="文书编号" prop="writ_number" >
							<el-input  
							  placeholder="请输入文书编号"  
							  v-model="addsend.writ_number"  
							  clearable  
							  >  
							</el-input>  
							</el-form-item>
							<el-form-item label="如何救济途径" prop="writ_function" >
								<el-input  
								  placeholder="请输入如何救济途径"  
								  v-model="addsend.writ_function"  
								  clearable  
								  >  
								</el-input>  
								</el-form-item>
					  <el-form-item label="文书类型" prop="writ_class" >
					  <el-select style="width: 70%;" v-model="addsend.writ_class" placeholder="文书类型">
						<el-option
						  v-for="item in writ_class"
						  :key="item.value"
						  :label="item.label"
						  :value="item.value">
						</el-option>
					  </el-select>  
					  </el-form-item>
					  <el-form-item label="发送内容" prop="send_text" >
					  <el-input  
					    type="textarea"
					    :rows="4"
						placeholder="请输入发送内容"  
						v-model="addsend.send_text"  
						clearable  
						>  
					  </el-input>  
					  </el-form-item>
					  <el-form-item label="备注" prop="remark" >
					  <el-input  
					    type="textarea"
					    :rows="3"
						placeholder="请输入备注"  
						v-model="addsend.remark"  
						clearable  
						>  
					  </el-input>  
					  </el-form-item>
					  <el-form-item label="送达人" prop="server" >
						<el-select
						style="width: 100%;"
						v-model="addsend.server"
						multiple
						filterable
						allow-create
						default-first-option
						placeholder="请输入送达人">
						<el-option
						  key=""
						  label="输入名字后点击回车"
						  value=""
						  disabled
						  >
						</el-option>
					  </el-select>
						</el-form-item>
					  <el-form-item label="上传文件" prop="receive" >
					  <el-upload
					  list-type="picture-card"
						accept=".png, .jpg, .jpeg"
						multiple
						class="upload-demo"  
						ref="upload"  
						action="/index/index/addfile"  
						:on-remove="upload_delete_img"  
						:on-success='upload_add_img'
						:before-upload="beforeimgUpload"
						:file-list="addsend.fileList"

						> 
						<i slot="default" class="el-icon-plus"></i>
						<div slot="file" slot-scope="{file}">
						<img
							class="el-upload-list__item-thumbnail"
							:src="client_url+file.path" alt=""
						> 
						<span class="el-upload-list__item-actions">
							<span
							  class="el-upload-list__item-preview"
							  @click="handlePictureCardPreview(file)"
							>
							  <i class="el-icon-zoom-in"></i>
							</span>

							<span
							class="el-upload-list__item-delete"
							@click="upload_delete_img(file)"
							>
							<i class="el-icon-delete"></i>
							</span>
						</div>
					  </el-upload>  
					  </el-form-item>
					  </el-form>
		  <span slot="footer" class="dialog-footer">
		    <el-button @click="addsendVisible = false">取 消</el-button>
		    <el-button type="primary" @click="submit_addsend()" :loading="btnloading">确 定</el-button>
		  </span>
		</el-dialog>
		
		<el-dialog :visible.sync="previewVisible">
			<img width="100%" :src="previewImageUrl" alt="">
		  </el-dialog>
		
	</div>
	


<script src="../../static/js/index.js"></script>
{include file="public/_footer"}

