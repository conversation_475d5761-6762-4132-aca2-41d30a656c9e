<?xml version="1.0" encoding="UTF-8"?>
<module type="WEB_MODULE" version="4">
  <component name="NewModuleRootManager">
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/app" isTestSource="false" packagePrefix="app\" />
      <sourceFolder url="file://$MODULE_DIR$/extend" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/spec" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/tests" isTestSource="true" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/league/flysystem-cached-adapter" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/league/mime-type-detection" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/simple-cache" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/league/flysystem" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/http-message" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/cache" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/log" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/container" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/topthink/think-filesystem" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/topthink/think-trace" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/topthink/think-helper" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/topthink/think-orm" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/topthink/framework" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-mbstring" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-php80" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/var-dumper" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/composer" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/topthink/think-view" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/topthink/think-template" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
  </component>
</module>