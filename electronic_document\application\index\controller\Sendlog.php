<?php

namespace app\index\controller;

use app\common\service\Download;
use think\Controller;
use think\Db;

class Sendlog extends Controller
{
    public function initialize()
    {
        if(!session('?user.username')){
            $this->redirect('index/login/index');
        }
    }
    public function select_send(){
        if(request()->isAjax()){
            $search=[
                'casenumber' => input('post.casenumber'),
                'title' => input('post.title'),
                'receive' => input('post.receive'),
                'contact' => input('post.contact'),
                'send_time' => input('post.send_time'),
                'receive_time' => input('post.receive_time'),
                'signed_status' => input('post.signed_status'),
                'writ_class' => input('post.writ_class'),
            ];
            $where=[];
            if ($search['casenumber']){
                $where[]=[
                    'c.casenumber','like', '%' . $search['casenumber'] . '%'
                ];
            }
            if ($search['title']){
                $where[]=[
                    'c.title','like', '%' . $search['title'] . '%'
                ];
            }
            if ($search['receive']){
                $where[]=[
                    'c.receive','like', '%' . $search['receive'] . '%'
                ];
            }
            if ($search['contact']){
                $where[]=[
                    'c.contact','like', '%' . $search['contact'] . '%'
                ];
            }
            if ($search['writ_class']&&$search['writ_class']!='all'){
                $where[]=[
                    's.class', '=' , $search['writ_class']
                ];
            }
            if ($search['signed_status']!='all'){
                $where[]=[
                    's.status', '=' , $search['signed_status']
                ];
            }
            if (session('user.level')!=0){
                $where[]=['c.create_user','=',session('user.username')];
            }
            if ($search['send_time']){
                // 解析时间字符串为 DateTime 对象
                $startTime = new \DateTimeImmutable($search['send_time'][0]);
                $endTime = new \DateTimeImmutable($search['send_time'][1]);
                // 将 DateTime 对象转换为数据库可以识别的格式（通常是字符串）
                $startTimeStr = $startTime->format('Y-m-d H:i:s');
                $endTimeStr = $endTime->format('Y-m-d H:i:s');
                $where[]=[
                    's.reach_time','>=', $startTimeStr
                ];
                $where[]=[
                    's.reach_time','<=', $endTimeStr
                ];
            }
            if ($search['receive_time']){
                // 解析时间字符串为 DateTime 对象
                $startTime = new \DateTimeImmutable($search['receive_time'][0]);
                $endTime = new \DateTimeImmutable($search['receive_time'][1]);
                // 将 DateTime 对象转换为数据库可以识别的格式（通常是字符串）
                $startTimeStr = $startTime->format('Y-m-d H:i:s');
                $endTimeStr = $endTime->format('Y-m-d H:i:s');
                $where[]=[
                    's.receive_time','>=', $startTimeStr
                ];
                $where[]=[
                    's.receive_time','<=', $endTimeStr
                ];
            }
            $sends=Db::name('send')
                ->alias('s')
                ->join('case c', 'c.casenumber = s.casenumber', 'LEFT')
                ->field('s.uid as s_uid ,s.* ,c.title,c.contact,c.receive')
                ->where('s.is_delete',0)
                ->where('c.is_delete',0)
                ->where($where)
                ->orderRaw("s.create_time DESC")
                ->select();
            $data=[];
            foreach ($sends as $send) {
                $uid = $send['uid'];
                $server = Db::name('server')
                    ->where('uid', $uid)
                    ->where('is_delete', 0)
                    ->order('create_time', 'desc')
                    ->column('server');
                // 组装结果
                $data[] = array_merge($send, [
                    'server' => $server ?? null,
                ]);
            }
            return json(['status' => 'success', 'message' => [
                'total'=>sizeof($data),
                'data'=>$data
            ]]);

        } else{
                        $log=Db::name('log')->insert([
                'username'=>session('user.username'),
                'name'=>session('user.name'),
                'action'=>'非法请求:'.request()->ip(),
                'create_time'=>date('Y-m-d H:i:s', time()),
            ]);
            return json(['status' => 'error', 'message' => '非法请求']);
        }
    }
    public function index()
    {
        return view();
    }
    public function download_case(){
        if(request()->isAjax()){
            $time=input('post.time');
            if (!$time){
                return json(['status' => 'error', 'message' => '请选择时间']);
            }else{
                $download=(new Download())->download('send',$time);
                if ($download){
                    //写操作日志
                    $log=Db::name('log')->insert([
                        'username'=>session('user.username'),
                        'name'=>session('user.name'),
                        'action'=>'导出送达详情',
                        'create_time'=>date('Y-m-d H:i:s', time()),
                    ]);
                    return json(['status' => 'success', 'message' => $download]);
                }else{
                    return json(['status' => 'error', 'message' => '导出失败']);
                }
            }
        } else{
                        $log=Db::name('log')->insert([
                'username'=>session('user.username'),
                'name'=>session('user.name'),
                'action'=>'非法请求:'.request()->ip(),
                'create_time'=>date('Y-m-d H:i:s', time()),
            ]);
            return json(['status' => 'error', 'message' => '非法请求']);
        }
    }
}