# 公安局电子文书送达系统说明书

## 1. 项目概述

公安局电子文书送达系统是一套基于Web技术的现代化文书送达解决方案，旨在提高公安工作效率，实现文书送达的电子化、规范化和智能化。系统支持案件管理、文书上传、电子送达、签名确认等功能，为公安干警和案件当事人提供便捷的文书传递和接收服务。

## 2. 系统架构

### 2.1 整体架构
- **服务端**：基于ThinkPHP 5.1框架，负责业务逻辑处理、数据存储和外部接口调用
- **客户端**：基于ThinkPHP 6.0框架，提供用户界面和交互功能
- **数据库**：MySQL，存储案件信息、用户数据和系统配置

### 2.2 目录结构
```
electronic_documents/
├── electronic_document/       # 服务端项目
│   ├── application/           # 应用目录
│   ├── config/                # 配置文件
│   ├── public/                # 公共资源
│   ├── route/                 # 路由配置
│   └── 开发文档/              # 项目文档
└── electronic_document_client/ # 客户端项目
    ├── app/                  # 应用目录
    ├── config/               # 配置文件
    ├── public/               # 公共资源
    └── view/                 # 视图文件
```

## 3. 主要功能

### 3.1 案件管理
- 创建、编辑和查询案件信息
- 案件编号、名称、当事人信息管理
- 案件状态跟踪

### 3.2 文书管理
- 支持多种格式的文书上传（文档、图片、音频等）
- 音频支持格式：MP3、M4A
- 图片支持格式：JPG、JPEG、PNG、GIF
- 文书分类和版本管理

### 3.3 电子送达
- 通过短信服务发送文书通知
- 支持多种文书类型：
  - 行政案件立案告知书
  - 不予立案告知书
  - 证据保全决定书
  - 行政处罚告知笔录
  - 鉴定意见通知书等
- 送达状态实时跟踪

### 3.4 电子签名
- 当事人在线签署文书
- 签名图片保存和验证

### 3.5 数据导出
- 案件信息导出为Excel报表
- 送达记录导出

### 3.6 日志管理
- 系统操作日志记录
- 用户行为审计

## 4. 技术栈

### 4.1 后端技术
- **框架**：ThinkPHP 5.1 (服务端)、ThinkPHP 6.0 (客户端)
- **数据库**：MySQL
- **第三方库**：
  - GuzzleHttp：HTTP客户端
  - PhpSpreadsheet：Excel文件处理

### 4.2 前端技术
- HTML/CSS/JavaScript
- 前端框架（根据视图文件推断）

## 5. 使用指南

### 5.1 系统登录
1. 打开系统登录页面
2. 输入用户名和密码
3. 点击登录按钮

### 5.2 案件创建
1. 登录系统后，进入案件管理页面
2. 点击"新增案件"按钮
3. 填写案件基本信息（案件编号、名称、当事人信息等）
4. 上传相关文书文件
5. 点击"保存"按钮

### 5.3 文书送达
1. 在案件详情页面，点击"送达文书"按钮
2. 选择要送达的文书
3. 确认送达方式（短信）
4. 点击"发送"按钮
5. 系统会自动发送短信通知当事人

### 5.4 查看送达状态
1. 在案件详情页面，查看送达记录
2. 可查看送达时间、送达方式、签收状态等信息

### 5.5 数据导出
1. 进入数据统计页面
2. 选择导出类型（案件或送达记录）
3. 设置时间范围
4. 点击"导出"按钮
5. 系统会生成Excel文件并下载

## 6. 常见问题

### 6.1 支持哪些文件格式？
- 图片：JPG、JPEG、PNG、GIF
- 音频：MP3、M4A
- 其他格式文件会被归类为"其他"类型

### 6.2 如何查看已送达的文书？
当事人收到短信后，点击短信中的链接，即可查看并签署文书。

### 6.3 如何修改案件信息？
在案件管理页面，找到需要修改的案件，点击"编辑"按钮进行修改。

### 6.4 系统支持多用户同时操作吗？
是的，系统支持多用户同时操作，并有完善的权限管理机制。

## 7. 注意事项
1. 案件编号一旦创建，不可修改
2. 重要文书请做好备份
3. 系统数据定期备份
4. 请勿上传与案件无关的文件
5. 请确保当事人联系方式准确，以便成功送达

---

**版本信息**：V1.0
**发布日期**：" . date('Y-m-d') . "
**开发团队**：公安局科技信息部门