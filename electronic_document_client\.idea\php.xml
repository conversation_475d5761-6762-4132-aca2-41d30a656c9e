<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="MessDetectorOptionsConfiguration">
    <option name="transferred" value="true" />
  </component>
  <component name="PHPCSFixerOptionsConfiguration">
    <option name="transferred" value="true" />
  </component>
  <component name="PHPCodeSnifferOptionsConfiguration">
    <option name="highlightLevel" value="WARNING" />
    <option name="transferred" value="true" />
  </component>
  <component name="PhpIncludePathManager">
    <include_path>
      <path value="$PROJECT_DIR$/vendor/league/flysystem-cached-adapter" />
      <path value="$PROJECT_DIR$/vendor/league/mime-type-detection" />
      <path value="$PROJECT_DIR$/vendor/psr/simple-cache" />
      <path value="$PROJECT_DIR$/vendor/league/flysystem" />
      <path value="$PROJECT_DIR$/vendor/psr/http-message" />
      <path value="$PROJECT_DIR$/vendor/psr/cache" />
      <path value="$PROJECT_DIR$/vendor/psr/log" />
      <path value="$PROJECT_DIR$/vendor/psr/container" />
      <path value="$PROJECT_DIR$/vendor/topthink/think-filesystem" />
      <path value="$PROJECT_DIR$/vendor/topthink/think-trace" />
      <path value="$PROJECT_DIR$/vendor/topthink/think-helper" />
      <path value="$PROJECT_DIR$/vendor/topthink/think-orm" />
      <path value="$PROJECT_DIR$/vendor/topthink/framework" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-mbstring" />
      <path value="$PROJECT_DIR$/vendor/symfony/polyfill-php80" />
      <path value="$PROJECT_DIR$/vendor/symfony/var-dumper" />
      <path value="$PROJECT_DIR$/vendor/composer" />
      <path value="$PROJECT_DIR$/vendor/topthink/think-view" />
      <path value="$PROJECT_DIR$/vendor/topthink/think-template" />
    </include_path>
  </component>
  <component name="PhpProjectSharedConfiguration" php_language_level="7.2" />
  <component name="PhpStanOptionsConfiguration">
    <option name="transferred" value="true" />
  </component>
  <component name="PsalmOptionsConfiguration">
    <option name="transferred" value="true" />
  </component>
</project>