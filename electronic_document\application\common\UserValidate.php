<?php

namespace app\common;

class UserValidate extends \think\Validate
{
// 定义验证规则
    protected $rule = [
        'username'      => 'require',
        'level'      => 'require|in:0,1',
        'name'      => 'require',
        'password'      => 'require',

    ];

    // 定义错误消息
    protected $message = [
        'username.require'     => '用户名不能为空',
        'level.in'     => '级别有误',
        'name.require'          => '姓名不能为空',
        'password.require'     => '密码不能为空',
    ];
}