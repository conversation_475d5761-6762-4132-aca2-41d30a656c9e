<?php

namespace app\index\controller;

use think\Controller;
use think\Db;

class Home extends Controller
{
    public function initialize()
    {
        if(!session('?user.username')){
            $this->redirect('index/login/index');
        }
    }
    public function index()
    {
        $createuser=[];
        if (session('user.level')!=0){
            $createuser=['create_user'=>session('user.username')];
        }
        //返回起始四个卡片
        $total=Db::name('case')->where('is_delete',0)->where($createuser)->count();
        $sends=Db::name('send')->where('is_delete',0)->where($createuser)->count();
        $receives=Db::name('send')->where('is_delete',0)->where($createuser)->where('status',1)->count();
        $user_total=Db::name('user')->where('is_delete',0)->count();
        if (session('user.level')!=0){
            $user_total=1;
        }
        return view('index',[
            'total'=>$total,
            'sends'=>$sends,
            'receives'=>$receives,
            'user_total'=>$user_total
        ]);
    }
    public function charts (){
        $createuser=[];
        if (session('user.level')!=0){
            $createuser=['create_user'=>session('user.username')];
        }
        $times=input('post.times');
        $where=[];
        if ($times){
            $startTime = new \DateTimeImmutable($times[0]);
            $endTime = new \DateTimeImmutable($times[1]);
            // 将 DateTime 对象转换为数据库可以识别的格式（通常是字符串）
            $startTimeStr = $startTime->format('Y-m-d H:i:s');
            $endTimeStr = $endTime->format('Y-m-d H:i:s');
            $where[]=['create_time','>=',$startTimeStr];
            $where[]=['create_time','<=',$endTimeStr];
        }else{
            return json(['status' => 'success','message'=>'请选择时间']);

        }
        //查询各个类别文书次数
        $whereStr = implode(' AND ', array_map(function($item) {
            return "`{$item[0]}` {$item[1]} '{$item[2]}'";
        }, $where));
        $sql = "SELECT 
                    SUM(CASE WHEN class = 1 THEN 1 ELSE 0 END) as count1,
                    SUM(CASE WHEN class = 2 THEN 1 ELSE 0 END) as count2,
                    SUM(CASE WHEN class = 3 THEN 1 ELSE 0 END) as count3,
                    SUM(CASE WHEN class = 4 THEN 1 ELSE 0 END) as count4,
                    SUM(CASE WHEN class = 5 THEN 1 ELSE 0 END) as count5,
                    SUM(CASE WHEN class = 6 THEN 1 ELSE 0 END) as count6,
                    SUM(CASE WHEN class = 7 THEN 1 ELSE 0 END) as count7,
                    SUM(CASE WHEN class = 8 THEN 1 ELSE 0 END) as count8,
                    SUM(CASE WHEN class = 9 THEN 1 ELSE 0 END) as count9,
                    SUM(CASE WHEN class = 10 THEN 1 ELSE 0 END) as count10,
                    SUM(CASE WHEN class = 11 THEN 1 ELSE 0 END) as count11,
                    SUM(CASE WHEN class = 12 THEN 1 ELSE 0 END) as count12,
                    SUM(CASE WHEN class = 13 THEN 1 ELSE 0 END) as count13
                FROM `send` ";
        $sql=$sql."WHERE {$whereStr}";
        if (session('user.level')!=0){
            $username=session('user.username');
            $sql=$sql."and create_user = '{$username}' ";
        }
        $resultData = Db::name('send')->query($sql);
        if (!empty($resultData)) {
            $chart_1 = array_values($resultData[0]); // 转换结果数组，去掉键名
            // 由于数据库字段名会是 count1, count2...，需要调整数组索引从0开始
            array_shift($chart_1);
        }
        //签收情况
        $total=Db::name('send')->where('is_delete',0)->where($createuser)->where($where)->count();
        $done=Db::name('send')->where('is_delete',0)->where($createuser)->where($where)->where('status',1)->count();

        //发送次数统计
        // 首先在send表中统计casenumber的出现次数，并取出前十名
        $subQuery = Db::table('send')
            ->field('casenumber, count(*) as count')
            ->group('casenumber')
            ->order('count', 'desc')
            ->where($createuser)
            ->where('is_delete',0)
            ->limit(10)
            ->buildSql();
        // 然后将统计结果与case表进行左连接查询
        $sends = Db::table('case')
            ->alias('c')
            ->join([$subQuery => 's'], 'c.casenumber = s.casenumber', 'INNER')
            ->field('c.title as name,s.count as count,c.receive,c.create_user')
            ->where('is_delete',0)
            ->order('s.count', 'desc')
            ->select();
        //返回结果
        $res=[
            'chart_1'=>$chart_1,
            'chart_2'=>['total'=>$total,'done'=>$done],
            'sends'=>$sends
        ];
        return json(['status' => 'success','message'=>$res]);

    }
}