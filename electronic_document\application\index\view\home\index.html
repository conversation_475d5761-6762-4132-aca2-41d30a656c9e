{include file="public/_head"}
<link rel="stylesheet" href="../../static/css/home.css">
<script src="../../static/js/echarts.min.js"></script>
<div id="app">
    <el-row id="banner" >
        <el-col class="banner" :span="6">
            <div class="menu">
                <div class="left">
                    <?xml version="1.0" encoding="UTF-8"?><svg width="50%" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><rect x="8" y="4" width="32" height="40" rx="2" stroke="#2F88FF" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/><path d="M16 4H25V20L20.5 16L16 20V4Z" fill="none" stroke="#2F88FF" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/><path d="M16 28H26" stroke="#2F88FF" stroke-width="4" stroke-linecap="round"/><path d="M16 34H32" stroke="#2F88FF" stroke-width="4" stroke-linecap="round"/></svg>
                </div>
                <div class="right">
                    <b>案件总数</b>
                    <p>{$total}</p>
                </div>
            </div>
        </el-col>
        <el-col class="banner" :span="6">
            <div class="menu">
                <div class="left">
                    <?xml version="1.0" encoding="UTF-8"?><svg width="50%" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M43 5L29.7 43L22.1 25.9L5 18.3L43 5Z" stroke="#2F88FF" stroke-width="4" stroke-linejoin="round"/><path d="M43.0001 5L22.1001 25.9" stroke="#2F88FF" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/></svg>
                </div>
                <div class="right">
                    <b>送达总数</b>
                    <p>{$sends}</p>
                </div>
            </div>
        </el-col>
        <el-col class="banner" :span="6">
            <div class="menu">
                <div class="left">
                    <?xml version="1.0" encoding="UTF-8"?><svg width="50%" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M44 24C44 35.0457 35.0457 44 24 44C18.0265 44 4 44 4 44C4 44 4 29.0722 4 24C4 12.9543 12.9543 4 24 4C35.0457 4 44 12.9543 44 24Z" fill="none" stroke="#2F88FF" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/><path d="M13.9999 26L20 32L33 19" stroke="#2F88FF" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/></svg>
                </div>
                <div class="right">
                    <b>签收总数</b>
                    <p>{$receives}</p>
                </div>
            </div>
        </el-col>
        <el-col class="banner" :span="6">
            <div class="menu">
                <div class="left">
                    <?xml version="1.0" encoding="UTF-8"?><svg width="50%" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M19 20C22.866 20 26 16.866 26 13C26 9.13401 22.866 6 19 6C15.134 6 12 9.13401 12 13C12 16.866 15.134 20 19 20Z" fill="none" stroke="#2F88FF" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/><path d="M32.6077 7C34.6405 8.2249 36.0001 10.4537 36.0001 13C36.0001 15.5463 34.6405 17.7751 32.6077 19" stroke="#2F88FF" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/><path d="M4 40.8V42H34V40.8C34 36.3196 34 34.0794 33.1281 32.3681C32.3611 30.8628 31.1372 29.6389 29.6319 28.8719C27.9206 28 25.6804 28 21.2 28H16.8C12.3196 28 10.0794 28 8.36808 28.8719C6.86278 29.6389 5.63893 30.8628 4.87195 32.3681C4 34.0794 4 36.3196 4 40.8Z" fill="none" stroke="#2F88FF" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/><path d="M43.9999 42.0001V40.8001C43.9999 36.3197 43.9999 34.0795 43.128 32.3682C42.361 30.8629 41.1371 29.6391 39.6318 28.8721" stroke="#2F88FF" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/></svg>
                </div>
                <div class="right">
                    <b>成员总数</b>
                    <p>{$user_total}</p>
                </div>
            </div>
        </el-col>
      </el-row>
      <div class="times">
        <el-date-picker
          v-model="times"
          type="daterange"
          align="right"
          unlink-panels
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :picker-options="pickerOptions">
        </el-date-picker>
      </div>
    <el-row class="charts">
        <el-col  class="chart" :span="24" id="chart_1">
        <!-- 案件类型饼图 -->
        </el-col>
        <el-col  class="chart" :span="12" style="border-radius: 10px 0 0 10px;" id="chart_2">
            <!-- 签收统计 -->
        </el-col>
        <el-col  class="chart" :span="12" id="chart_2">
            <!-- 榜单 -->
            <template>
                <el-table
                  :data="sends"
                  style="width: 100%"
                  >
                  <el-table-column label="送达次数排行">
                  <el-table-column
                    type="index"
                    label="序号"
                    >
                  </el-table-column>
                  <el-table-column
                  prop="name"
                  label="案件名称"
                  >
                </el-table-column>
                  <el-table-column
                    prop="receive"
                    label="接收人员"
                    >
                  </el-table-column>
                  <el-table-column
                    prop="count"
                    label="送达次数">
                  </el-table-column>
                  {if session('user.level') eq 0}
                  <el-table-column
                  prop="create_user"
                  label="负责部门">
                </el-table-column>
                {/if}
                </el-table>
              </template>
        </el-col>
    </el-row>
</div>
<script src="../../static/js/home.js"></script>


{include file="public/_footer"}

