<?php

namespace app\controller;

use think\facade\Request;
use think\facade\Db;

class Recorded
{
    public function index($uid = null){
        if (is_null($uid)) {
            return json(['status' => 'error', 'message' => '参数异常']);
        }else{
            $status = Db::name('send')->where('is_delete', 0)->where('uid', $uid)->value('status');
            $signature = Db::name('file')->where('is_delete', 0)->where('uid', $uid)->where('class',2)->value('name');
            $recorded = Db::name('file')->where('is_delete', 0)->where('uid', $uid)->where('class',3)->value('name');

            if ($status&&$signature&&$recorded){
                return json(['status' => 'error', 'message' => '您已完成录音确认']);
            }else{
                $casenumber = Db::name('send')->where('is_delete', 0)->where('uid', $uid)->value('casenumber');
                $data=Db::name('case')->where('is_delete',0)->where('casenumber',$casenumber)->field('receive,contact')->find();
                $data['time']=date('Y年m月d日');
                return view('index',[
                    'data'=>$data,
                ]);
            }

        }
    }
    public function add_recording(Request $request)
    {
        $uid=input('post.uid');
        $send=Db::name('send')->where('is_delete',0)->where('uid',$uid)->find();
        if ($send){
            //获取音频
            // 获取表单上传文件
            $file =Request::file('audioFile');
            // 指定保存目录
            $class = 'audiofile';
            $uploadDir = app()->getRootPath() . '/public/upload/' . $class . '/';
            // 确保上传目录存在
            if (!is_dir($uploadDir)) {
                mkdir($uploadDir, 0777, true);
            }
            // 生成文件名
            $fileName = md5(uniqid()) . '.wav';
            // 注意：这里只需要提供目录，不需要提供完整的文件路径
            if ($file){
                $info = $file->move($uploadDir, $fileName);
                // 检查文件写入是否成功
                if ($info === false) {
                    // 文件写入失败，记录错误并返回相应的消息
                    // 这里可以添加错误日志记录代码
                    return json([
                        'status' => 'error',
                        'message' => '文件写入失败，请检查服务器日志以获取更多信息。'
                    ]);
                } else {
                    $filepath='/upload/' . $class . '/' . $fileName;
                    $signature=Db::name('send')
                        ->where('is_delete',0)
                        ->where('uid',$uid)
                        ->update([
                            'status'=>1,
                            'receive_time'=>date('Y-m-d H:i:s', time()),
                        ]);
                    $file=Db::name('file')
                        ->insert([
                            'casenumber'=>$send['casenumber'],
                            'uid'=>$uid,
                            'class'=>3,
                            'name'=> $fileName,
                            'path'=>  $filepath,
                            'create_time'=>date('Y-m-d H:i:s', time()),
                        ]);
                    if ($signature&&$file){
                        return json([
                            'status' => 'success',
                            'message' => '确认成功'
                        ]);
                    }else{
                        return json([
                            'status' => 'error',
                            'message' => '上传失败'
                        ]);
                    }
                }
            }else{
                return json([
                    'status' => 'error',
                    'message' => '没有文件被上传'
                ]);
            }

        }else{
            return json([
                'status' => 'error',
                'message' => '项目不存在'
            ]);
        }


    }
}