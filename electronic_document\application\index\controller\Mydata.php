<?php

namespace app\index\controller;

use think\Controller;

class Mydata extends Controller
{
    public function initialize()
    {
        if(!session('?user.username')){
            $this->redirect('index/login/index');
        }
    }

    public function index(){
        return view();
    }
    public function select_mine(){
        $data=[
            'username'=>session('user.username'),
            'name'=>session('user.name'),
            'level'=>session('user.level'),
        ];
        if ($data['level']==0){
            $data['level']='超级管理员';
        }elseif ($data['level']==1){
            $data['level']='管理员';
        }
        return json(['status' => 'success', 'message' => $data]);
    }

}