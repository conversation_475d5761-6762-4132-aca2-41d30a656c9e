<?php
namespace app\controller;

use app\BaseController;
use think\facade\Db;
class Index extends BaseController
{
    public function index($uid = null){
        if (is_null($uid)) {
            return json(['status' => 'error', 'message' => '参数异常']);
        }else{
            $text = Db::name('send')->where('is_delete', 0)->where('uid', $uid)->value('text');
            $imgs=Db::name('file')->where('class',1)->where('is_delete',0)->where('uid',$uid)->orderRaw("create_time ASC")->column('path');
            $data = Db::name('case')
                ->alias('c') // 为 'case' 表设置别名 'c'
                ->join('send s', 'c.casenumber = s.casenumber', 'LEFT') // 修正连接条件，使用正确的别名和字段
                ->field('s.writ_function,s.writ_number, s.writ_title, c.receive, c.contact') // 确保字段名正确，并且与别名匹配
                ->where('s.uid', $uid) // 过滤 'send' 表中 uid 字段
                ->where('c.is_delete', 0) // 过滤 'case' 表中未删除的记录
                ->where('s.is_delete', 0) // 过滤 'send' 表中未删除的记录
                ->find(); // 执行查询并返回单条记录
        }
        if (!$text){
            return json(['status' => 'error', 'message' => '参数异常']);
        }
        //判断是否已经签署

        $status = Db::name('send')->where('is_delete', 0)->where('uid', $uid)->value('status');
        $signature = Db::name('file')->where('is_delete', 0)->where('uid', $uid)->where('class',2)->value('path');
        $recorded = Db::name('file')->where('is_delete', 0)->where('uid', $uid)->where('class',3)->value('path');

        //确定应该去哪部分进行签署
        $step='signature';
        if ($signature){
            $step='recorded';
        }

        if ($status&&$signature&&$recorded){
            $step='success';
        }
        return view('index',[
            'text'=>$text,
            'imgs'=>$imgs,
            'step'=>$step,
            'signature'=>$signature,
            'recorded'=>$recorded,
            'status'=>$status,
            'data'=>$data
        ]);
    }


}
