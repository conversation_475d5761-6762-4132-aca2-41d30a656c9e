<?php

namespace app\controller;

use app\Request;
use think\facade\Db;

class Signature
{
    public function index($uid = null){
        if (is_null($uid)) {
            return json(['status' => 'error', 'message' => '参数异常']);
        }else{
            $status = Db::name('send')->where('is_delete', 0)->where('uid', $uid)->value('status');
            $signature = Db::name('file')->where('is_delete', 0)->where('uid', $uid)->where('class',2)->value('name');

            if ($signature){
                return json(['status' => 'error', 'message' => '您已完成签名确认']);
            }else{
                return view('index',[
                    'status'=>$status,
                ]);
            }
        }

    }
    public function signature(Request $request)
    {
        $uid=input('post.uid');
        $send=Db::name('send')->where('is_delete',0)->where('uid',$uid)->find();
        if ($send){
            // 获取Base64编码的图片数据
            $base64Data = $request->post('data');
//            return $base64Data;
            // 验证数据是否存在且格式正确
            if (!$base64Data || !preg_match('/^data:image\/(\w+);base64,(.+)$/', $base64Data, $matches)) {
                return json(['error' => 'Invalid image data'], 400);
            }
            // 提取Base64编码的图片数据和MIME类型
            $mimeType = $matches[1];
            $base64Image = $matches[2];
            // 替换空格为+号（虽然base64_decode()通常可以处理空格，但为了安全起见还是替换掉）
            $base64Image = str_replace(' ', '+', $base64Image);
            // 解码Base64数据为二进制数据
            $imageData = base64_decode($base64Image);
            // 设置文件后缀，这里根据MIME类型来确定后缀可能更合适
            $suffix = 'png'; // 默认设置为png，但可以根据$mimeType来动态设置
            if ($mimeType === 'jpeg') {
                $suffix = 'jpg';
            }
            $class = 'signature';
            $uploadDir =  app()->getRootPath().'/public/upload/' . $class . '/';
            // 确保上传目录存在
            if (!is_dir($uploadDir)) {
                mkdir($uploadDir, 0777, true);
            }
            // 生成文件名
            $fileName = md5(uniqid()) . '.' . $suffix;
            $filePath = $uploadDir . $fileName;
            // 尝试将二进制数据写入文件
            $result = file_put_contents($filePath, $imageData);
// 检查文件写入是否成功
            if ($result === false) {
                // 文件写入失败，记录错误并返回相应的消息
                // 这里可以添加错误日志记录代码
                return json([
                    'status' => 'error',
                    'message' => '文件写入失败，请检查服务器日志以获取更多信息。'
                ]);
            } else {
                $filepath='/upload/' . $class . '/' . $fileName;
                $file=Db::name('file')
                    ->insert([
                        'casenumber'=>$send['casenumber'],
                        'uid'=>$uid,
                        'class'=>2,
                        'name'=> $fileName,
                        'path'=>  $filepath,
                        'create_time'=>date('Y-m-d H:i:s', time()),
                    ]);
                if ($file){
                    return json([
                        'status' => 'success',
                        'message' => '签署成功'
                    ]);
                }else{
                    return json([
                        'status' => 'error',
                        'message' => '上传签署失败'
                    ]);
                }
            }
        }else{
            return json([
                'status' => 'error',
                'message' => '项目不存在'
            ]);
        }
    }
}