{include file="public/_head"}
<link rel="stylesheet" href="../../static/css/user.css">
	<div id="app">
		<div id="search">
		<el-row>  
				<el-col :span="7" class="search">
                    <label class="search_label">用户名</label>
                    <el-input  
                    placeholder="请输入"  
                    v-model="search.username"  
                    clearable  
                    style="flex: 1;">  
                    </el-input>  
				</el-col>
				<el-col :span="7" class="search">
                    <label class="search_label">姓名</label>
                    <el-input  
                    placeholder="请输入"  
                    v-model="search.name"  
                    clearable  
                    style="flex: 1;">  
                    </el-input>  
				</el-col>
                <el-col :span="7" class="search">
					<label class="search_label">级别</label>
					<el-select v-model="search.level" placeholder="全部">
					  <el-option
						v-for="item in levels"
						:key="item.value"
						:label="item.label"
						:value="item.value">
					  </el-option>
					</el-select>  
  			    </el-col>
                <el-col :span="3" class="search buttons">
                    <el-button type="primary" @click="select_user">查询</el-button>
                    <!-- <el-button @click="adduserVisible=true">新增</el-button> -->
                </el-col>
			</el-row>  
			
		</div>
		<div class="interval"></div>
		<div id="list">
			  <el-table
				id="table"
				stripe
			    :data="paginatedTableData"
          style="width: 100%;height: 75vh;"

				>
			    <el-table-column
			      prop="username"
			      label="用户名"
				  align="center"
			      >
			    </el-table-column>
			    <el-table-column
			      prop="name"
			      label="姓名"
				  align="center"
			      >
			    </el-table-column>
			    <el-table-column
			      prop="level"
			      label="级别"
				  align="center"
			      >
                  <template slot-scope="scope">
                    <template v-if="scope.row.level == 0">  
                      <el-tag type="success">超级管理员</el-tag>  
                    </template>  
                    <template v-else-if="scope.row.level == 1">  
                      <el-tag type="warning">管理员</el-tag>  
                    </template>
              </template>
			    </el-table-column>
                <el-table-column
                prop="status"
                label="状态"
                align="center"
                >
                <template slot-scope="scope">
                    <template v-if="scope.row.status == 0">  
                      <el-tag type="success">正常</el-tag>  
                    </template>  
                    <template v-else-if="scope.row.status == 1">  
                      <el-tag type="danger">停用</el-tag>  
                    </template>
              </template>
              </el-table-column>
			    <el-table-column
			      label="操作"
				  align="center"
			      >
			      <template slot-scope="scope">
			        <el-button
			          @click.native.prevent="user_detail(scope.row)"
			          type="text"
			          size="small">
			          详情
			        </el-button>
			      </template>
			    </el-table-column>
			  </el-table>
			  
			      <el-pagination
					id="pagination"
					background
			        @size-change="handleSizeChange"
			        @current-change="handleCurrentChange"
			        :current-page="currentPage"
			        :page-sizes="[10, 20, 30, 50]"
			        :page-size="pageSize"
			        layout="total, sizes, prev, pager, next, jumper"
			        :total="send_total">
			      </el-pagination>
		</div>
		
		<el-dialog
		  title="详情"
		  :close-on-click-modal="false"
		  :visible.sync="detailVisible"
		  :modal-append-to-body="false"
            width="40%"
		  >
          <el-form :hide-required-asterisk="true" ref="detailuser"  :rules="gai_rules"  :model="detailuser"  style="padding-right: 3rem;">
            <el-form-item  label="用户名" prop="username" >  
              <el-input  
                placeholder="请输入用户名"  
                v-model="detailuser.username"  
                clearable
                readonly  
                >  
              </el-input>  
            </el-form-item>  
            <el-form-item label="姓名" prop="name" >  
              <el-input  
                placeholder="请输入姓名"  
                v-model="detailuser.name"  
                clearable  
                >  
              </el-input>  
            </el-form-item>  
              
            <el-form-item label="密码" prop="password" >  
              <el-input  
                  type="password"
                placeholder="不修改可不填"  
                v-model="detailuser.password"  
                clearable  
                >  
              </el-input>  
            </el-form-item>  
            <el-form-item label="级别" prop="level" >  
              <el-radio-group v-model="detailuser.level">  
                <el-radio label="1">管理员</el-radio>  
                <el-radio label="0">超级管理员</el-radio>  
              </el-radio-group>  
            </el-form-item>  

            </el-form>
		  <span slot="footer" class="dialog-footer"> 

			 <el-button v-if="detailuser.status == '0'" type="danger" @click="open('delete')">删 除</el-button>
             <el-button v-else-if="detailuser.status == '1'" type="success" @click="open('open')">启 用</el-button>
			<el-button type="primary" @click="open('xiugai')">修 改</el-button>  
			<el-button type="primary" @click="detailVisible=false">关 闭</el-button>  
		  </span> 
				   
		</el-dialog>
		
        <!-- <el-dialog
        title="新增成员"
        :close-on-click-modal="false"
        :visible.sync="adduserVisible"
        width="40%"
        center>
                <el-form :hide-required-asterisk="true" ref="adduser"  :rules="rules"  :model="adduser"  style="padding-right: 3rem;">
                  <el-form-item  label="用户名" prop="username" >  
                    <el-input  
                      placeholder="请输入用户名"  
                      v-model="adduser.username"  
                      clearable  
                      >  
                    </el-input>  
                  </el-form-item>  
                  <el-form-item label="姓名" prop="name" >  
                    <el-input  
                      placeholder="请输入姓名"  
                      v-model="adduser.name"  
                      clearable  
                      >  
                    </el-input>  
                  </el-form-item>  
                    
                  <el-form-item label="密码" prop="password" >  
                    <el-input  
                        type="password"
                      placeholder="请输入密码"  
                      v-model="adduser.password"  
                      clearable  
                      >  
                    </el-input>  
                  </el-form-item>  
                  <el-form-item label="级别" prop="level" >  
                    <el-radio-group v-model="adduser.level">  
                      <el-radio label="1">管理员</el-radio>  
                      <el-radio label="0">超级管理员</el-radio>  
                    </el-radio-group>  
                  </el-form-item>  

                  </el-form>
                <span slot="footer" class="dialog-footer">  
                  <el-button @click="adduserVisible = false">取 消</el-button>  
                  <el-button type="primary" @click="submit_adduser()" :loading="btnloading">确 定</el-button>  
                </span> 
                 
      </el-dialog> -->
	</div>
	

	
<script src="../../static/js/user.js"></script>
{include file="public/_footer"}

