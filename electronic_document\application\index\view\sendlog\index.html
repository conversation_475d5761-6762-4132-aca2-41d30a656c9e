{include file="public/_head"}
<link rel="stylesheet" href="../../static/css/sendlog.css">
	<div id="app">
		<div id="search">
		<el-row>  
				<el-col :span="4" class="search">
								  <label class="search_label">案件编号</label>
								  <el-input  
									placeholder="请输入"  
									v-model="search.casenumber"  
									clearable  
									style="flex: 1;">  
								  </el-input>  
				</el-col>
				<el-col :span="4" class="search">
								  <label class="search_label">案件名称</label>
								  <el-input  
									placeholder="请输入"  
									v-model="search.title"  
									clearable  
									style="flex: 1;">  
								  </el-input>  
				</el-col>
				<el-col :span="4" class="search">
								  <label class="search_label">接收人姓名</label>
								  <el-input  
									placeholder="请输入"  
									v-model="search.receive"  
									clearable  
									style="flex: 1;">  
								  </el-input>  
				</el-col>
				<el-col :span="4" class="search">
								  <label class="search_label">接收人联系方式</label>
								  <el-input  
									placeholder="请输入"  
									v-model="search.contact"  
									clearable  
									style="flex: 1;">  
								  </el-input>  
				</el-col>
				<el-col :span="4" class="search">
					<label class="search_label">文书类型</label>
					<el-select style="width: 70%;" v-model="search.writ_class" placeholder="全部">
					  <el-option
						v-for="item in writ_class"
						:key="item.value"
						:label="item.label"
						:value="item.value">
					  </el-option>
					</el-select>  
  			</el-col>
			  <el-col :span="4" class="search">
				<label class="search_label">签收状态</label>
				<el-select style="width: 70%;" v-model="search.signed_status" placeholder="全部">
				  <el-option
					v-for="item in signed_status"
					:key="item.value"
					:label="item.label"
					:value="item.value">
				  </el-option>
				</el-select>  
		  </el-col>
			</el-row>  
			
			<el-row>
				<el-col :span="8" class="search">
					<label class="search_label">送达时间</label>
					<div class="block">
						<el-date-picker
						  v-model="search.send_time"
						  type="daterange"
						  align="right"
						  unlink-panels
						  range-separator="至"
						  start-placeholder="开始日期"
						  end-placeholder="结束日期"
						  :picker-options="pickerOptions">
						</el-date-picker>
					  </div>
		  </el-col>
			<el-col :span="8" class="search">
							  <label class="search_label">签收时间</label>
							  <div class="block">
							      <el-date-picker
							        v-model="search.receive_time"
							        type="daterange"
							        align="right"
							        unlink-panels
							        range-separator="至"
							        start-placeholder="开始日期"
							        end-placeholder="结束日期"
							        :picker-options="pickerOptions">
							      </el-date-picker>
							    </div>
			</el-col>
			<el-col :span="8" class="search">
				<el-button type="primary" @click="select_send">查询</el-button>
				<el-button @click="downloadVisible=true">导出</el-button>
			</el-col>
			</el-row>
		</div>
		<div class="interval"></div>
		<div id="list">
			  <el-table
				id="table"
				stripe
			    :data="paginatedTableData"
				style="width: 100%;height: 75vh;"
				:max-height="table_height"
				@selection-change="handleSelectionChange"
				@select-all="handleSelectAll"
				>
				<el-table-column
				  label="序号"
				  type="index"
				  align="center"
				  min-width="30"
				  >
				</el-table-column>
			    <el-table-column
			      prop="casenumber"
			      label="案件编号"
				  align="center"
				  min-width="100"
			      >
			    </el-table-column>
			    <el-table-column
			      prop="title"
			      label="案件名称"
				  align="center"
				  min-width="100"

			      >
			    </el-table-column>
			    <el-table-column
			      prop="receive"
			      label="接收人姓名"
				  align="center"
				  min-width="100"

			      >
			    </el-table-column>
			    <el-table-column
			      prop="contact"
			      label="接收人联系方式"
				  min-width="120"

				  align="center"
			    >
			    </el-table-column>
				<el-table-column
				label="文书名称"
				min-width="100"

				prop="writ_title"
				align="center"
				>
			  </el-table-column>
			  <el-table-column
			  label="文书编号"
			  min-width="100"

			  prop="writ_number"
			  align="center"
			  >
			</el-table-column>
			<el-table-column
			label="如何救济途径"
			min-width="120"

			prop="writ_function"
			align="center"
			>
		  </el-table-column>
			    <el-table-column
			      prop="class"
			      label="文书类型"
				  min-width="100"

				  align="center"
			      >
				  <template slot-scope="scope" >
					  <!-- <el-tag type="success"> -->
						{{ 
							scope.row.class == 1 ? '行政案件立案告知书' :
							scope.row.class == 2 ? '不予立案告知书' :
							scope.row.class == 3 ? '证据保全决定书' :
							scope.row.class == 4 ? '行政处罚告知笔录' :
							scope.row.class == 5 ? '鉴定意见通知书' :
							scope.row.class == 6 ? '不予行政处罚决定书' :
							scope.row.class == 7 ? '行政处罚决定书' :
							scope.row.class == 8 ? '责令限期改正通知书' :
							scope.row.class == 9 ? '没收违法所得、非法财物清单' :
							scope.row.class == 10 ? '收缴物品清单' :
							scope.row.class == 11 ? '追缴物品清单' :
							scope.row.class == 12 ? '催告书' :
							scope.row.class == 13 ? '终止案件调查决定书' :
							'未知' 
						  }}
					  <!-- </el-tag> -->
				  </template>
			    </el-table-column>
				<el-table-column  
				  prop="text"  
				  label="发送内容"  
				  min-width="100"

				  align="center"  
				>  
				  <template slot-scope="scope">  
					<el-tooltip  
					  class="item"  
					  effect="dark"  
					  :content="scope.row.text || '无内容'"
					  placement="top"  
					>  
					  <span>{{ (scope.row.text || '').slice(0, 20) }}...</span> <!-- 使用空字符串作为默认值 -->  
					</el-tooltip>  
				  </template>  
				</el-table-column>
				<el-table-column
				  prop="uploadwrit"
				  label="上传文书"
				  min-width="80"

				  align="center"
				  >
					  <template slot-scope="scope">  
						<template v-if="scope.row.uploadwrit == 0">  
						  <el-tag type="warning">未上传</el-tag>  
						</template>  
						<template v-else-if="scope.row.uploadwrit == 1">  
						  <el-tag type="success">已上传</el-tag>  
						</template>  
						<template v-else>  
						  <el-tag type="danger">未知</el-tag>  
						</template>  
					  </template>  
				</el-table-column>
				<el-table-column
				  prop="reach_time"
				  label="送达时间"
				  min-width="100"

				  align="center"
				  >
				</el-table-column>
				<el-table-column
				  prop="receive_time"
				  label="签收时间"
				  align="center"
				  min-width="100"

				  >
				</el-table-column>
				<el-table-column
				  prop="status"
				  label="状态"
				  align="center"
				  min-width="80"

				  >
				  <template slot-scope="scope">
						<template v-if="scope.row.status == 0">  
						  <el-tag type="warning">未签收</el-tag>  
						</template>  
						<template v-else-if="scope.row.status == 1">  
						  <el-tag type="success">已签收</el-tag>  
						</template>  
						<template v-else>  
						  <el-tag type="danger">未知</el-tag>  
						</template>  
				  </template>  
				</el-table-column>
				<el-table-column
				label="备注"
				prop="remark"
				min-width="50"

				align="center"
				>
			  </el-table-column>
				<el-table-column
				label="送达人"
				min-width="150"

				align="center"
				>
				<template slot-scope="scope">  
				<el-tag
				v-for="(item, index) in scope.row.server"
				:key="index"
				type="success"
			  >
				{{ item }}
			  </el-tag>
				</template>  
			  </el-table-column>
			  {if session('user.level') eq 0}
			  <el-table-column
			  prop="create_user"
			  min-width="100"

			  label="负责部门">
			</el-table-column>
			{/if}
			    <el-table-column
			      label="操作"
				  align="center"
				  
				  min-width="50"
			      >
			      <template slot-scope="scope">
			        <el-button
			          @click.native.prevent="detail(scope.row)"
			          type="text"
			          size="small">
			          详情
			        </el-button>
			      </template>
			    </el-table-column>
			  </el-table>
			  
			      <el-pagination
					id="pagination"
					background
			        @size-change="handleSizeChange"
			        @current-change="handleCurrentChange"
			        :current-page="currentPage"
			        :page-sizes="[10, 20, 30, 50]"
			        :page-size="pageSize"
			        layout="total, sizes, prev, pager, next, jumper"
			        :total="send_total">
			      </el-pagination>
		</div>
		
		<!-- <el-dialog
		  title="详情"
		  :close-on-click-modal="false"
		  :visible.sync="detailVisible"
		  width="40rem"
		  :modal-append-to-body="false"
		  >
			<iframe  
        :src="iframeSrc"  
        frameborder="0"  
        style="width: 100%; height: 70vh; border: none;"  
      ></iframe> 
		  <span slot="footer" class="dialog-footer">  
			<el-button type="primary" @click="detailVisible=false">关 闭</el-button>  
		  </span> 
				   
		</el-dialog> -->
		
		<el-dialog
		title="导出案件"
		:close-on-click-modal="false"
		:visible.sync="downloadVisible"
		width="40%"
		center>
		<div style="text-align: center;width: 100%;">
		<label class="search_label">请选择创建时间</label>
			<el-date-picker
			  v-model="downloadcase_time"
			  type="daterange"
			  align="right"
			  unlink-panels
			  range-separator="至"
			  start-placeholder="开始日期"
			  end-placeholder="结束日期"
			  :picker-options="pickerOptions">
			</el-date-picker>
		</div>
		<span slot="footer" class="dialog-footer">  
			<el-button @click="downloadVisible = false">取 消</el-button>  
			<el-button type="primary" @click="download_case()" :loading="btnloading">确 定</el-button>  
		</span> 
				 
	  </el-dialog>
	</div>
	

	
<script src="../../static/js/sendlog.js"></script>
{include file="public/_footer"}

