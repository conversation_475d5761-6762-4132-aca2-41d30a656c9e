{include file="public/_head"}
<link rel="stylesheet" href="../../static/css/mydata.css">
	<div id="app">
        <div class="out">
        <el-descriptions class="margin-top" title="个人信息" :column="1"  border>
            <template slot="extra">
                <el-button type="primary" @click="passwordrVisible = true">修改密码</el-button>
              </template>
            <el-descriptions-item>
              <template slot="label">
                <i class="el-icon-user"></i>
                用户名
              </template>
              {{mine.username}}

            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                <i class="el-icon-tickets"></i>
                姓名
              </template>
              {{mine.name}}
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                <i class="el-icon-s-promotion"></i>
                级别
              </template>
              <el-tag >{{mine.level}}</el-tag>
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <el-dialog
title="修改密码"
:close-on-click-modal="false"
:visible.sync="passwordrVisible"
width="40%"
center>
        <el-form :hide-required-asterisk="true" ref="password"  :rules="rules"  :model="password"  style="padding-right: 3rem;">
          <el-form-item  label="原密码" prop="old" >  
            <el-input
            type="password"  
              placeholder="请输入原密码"  
              v-model="password.old"  
              clearable  
              >  
            </el-input>  
          </el-form-item>  
          <el-form-item  label="新密码" prop="new" >  
            <el-input  
            type="password"  
              placeholder="请输入新密码"
              v-model="password.new"  
              clearable  
              >  
            </el-input>  
          </el-form-item>  
          <el-form-item  label="重复密码" prop="repeat" >  
            <el-input  
            type="password"  
              placeholder="请重复新密码"
              v-model="password.repeat"  
              clearable  
              >  
            </el-input>  
          </el-form-item>  
          </el-form>
        <span slot="footer" class="dialog-footer">  
          <el-button @click="passwordrVisible = false">取 消</el-button>  
          <el-button type="primary" @click="xiugai_password()" >确 定</el-button>  
        </span> 
         
</el-dialog>
	</div>
	

		


	
<script src="../../static/js/mydata.js"></script>
{include file="public/_footer"}

