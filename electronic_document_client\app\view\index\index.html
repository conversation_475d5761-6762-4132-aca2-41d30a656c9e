{include file="public/_head"}
<link rel="stylesheet" href="../../static/css/index.css">
	<div id="app">
    <el-row :gutter="10">
      <el-col :xs="24" :lg="8">
        <div class="basic">
          <h3 class="title">案件信息</h3>
          <el-descriptions class="margin-top" :column="1"  :colon="true">
              <el-descriptions-item>
                <template slot="label">
                  <i class="el-icon-user-solid"></i>
                  受送达人姓名
                </template>
                {$data['receive']}
  
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label">
                  <i class="el-icon-phone"></i>
                  联系方式
                </template>
                {$data['contact']}

              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label">
                  <i class="el-icon-s-order"></i>
                  文书名称
                </template>
                {$data['writ_title']}

              </el-descriptions-item>
              <el-descriptions-item>
                  <template slot="label">
                    <i class="el-icon-s-promotion"></i>
                    文书编号
                  </template>
                  {$data['writ_number']}
                </el-descriptions-item>
            </el-descriptions>
          </div>
          <div class="basic">
              <h3 class="title">通知内容</h3>
              <p id="text">{$text}</p>
          </div>
      </el-col>
      <el-col :xs="24"  :lg="8">
        <div class="basic">
          <h3 class="title">相关文书</h3>
      <el-row  class="row-class imgs"  :gutter="20" align="top"> 
              {foreach $imgs as $img}
              <!-- <el-col class="col-class imgs" :span="8" > -->
              <el-image
              :preview-src-list={:json_encode($imgs)}
              style="width: 30%;margin: auto;"
                src="{$img}"
                ></el-image>
              <!-- </el-col> -->
              {/foreach}
      </el-row>
      </div>
      <div class="basic">
          <h3 class="title">如何救济途径</h3>
          <p id="text">{$data.writ_function}</p>
      </div>
      </el-col>
      <el-col :xs="24"  :lg="8">
        {if $signature}
        <div class="basic">
            <h3 class="title">签名确认</h3>
        <el-row  class="row-class"  align="top"> 
        <el-col class="col-class" :span="24" >
          <el-image
          :preview-src-list="['{$signature}']"
          style="width: 100%;height: 100%;"
            src="{$signature}"
            fit="fill"></el-image>
                </el-col>
        </el-row>
        </div>
        {/if}
        {if $recorded}

        <div class="basic">
            <h3 class="title">录音确认</h3>
        <el-row  class="row-class"  align="top"> 
        <el-col class="col-class" :span="24" >
                    <audio src="{$recorded}" controls="controls"></audio>
                </el-col>
        </el-row>
        </div>
        {/if}
      </el-col>
      <el-col class="buttons" :xs="24">
        {if $step=='success'}
        <el-button disabled type="primary">已收到送达内容</el-button>
{else}
        <el-button @click="tourl('{$step}')" type="primary">已收到送达内容</el-button>
{/if}
<el-button id="print" @click="print()" type="primary">打印</el-button>

      </el-col>
    </el-row>
    


    </div>
<script src="../../static/js/index.js"></script>
{include file="public/_footer"}