<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
		<title>登录</title>
		<script src="../../static/js/vue2.js"></script>
		<!-- <link rel="stylesheet" href="../../static/css/<EMAIL>"> -->
		<script src="../../static/js/<EMAIL>"></script>
		<script src="../../static/js/axios.min.js"></script>
		<link rel="stylesheet" href="../../static/css/login.css">
		<!-- <script src="https://cdn.jsdelivr.net/npm/vue@2/dist/vue.js"></script> -->
		<link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
		<!-- <script src="https://unpkg.com/element-ui/lib/index.js"></script> -->
	</head>
	<body>
		<div id="app">
			<div id="card">
				<div class="col">
					  <img id="banner" src="../../static/image/login/banner.png" alt="">
				</div>
				<div class="col" id="inline">
					  <h1>欢迎登录</h1>
					  <el-form :rules="rules" hide-required-asterisk:true :model="login" ref="login"> 
						  <el-form-item label="" prop="username">  
					  <el-input  
							placeholder="请输入账号"  
							v-model="login.username"  
							clearable  
							style="flex: 1;margin-top:1rem"
							prefix-icon="el-icon-user"
							class="lgoin">  
					  </el-input> 
					  </el-form-item>  
					      <el-form-item label="" prop="password">  
					  <el-input
					  						type='password'
					  						show-password
					  						placeholder="请输入密码"  
					  						v-model="login.password"  
											prefix-icon="el-icon-lock"
					  						clearable  
					  						style="flex: 1;margin-top:1rem"
					  						class="lgoin"
					  						>  
					  </el-input> 
					  </el-form-item>
					  						<el-checkbox style="margin-top:1rem" v-model="login.remember">记住密码</el-checkbox>
					  <el-button @click="submit_login" id="login" type="primary"  style="width: 100%;">登录</el-button>
					  </el-form>
				</div>
			</div>
		</div>
		<script src="../../static/js/login.js"></script>
	</body>
</html>

